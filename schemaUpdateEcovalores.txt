CREATE TABLE announcement_group (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, sessions_announcement_id INT DEFAULT NULL, type_money_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, company_profile VARCHAR(255) DEFAULT NULL,
 code VARCHAR(100) DEFAULT NULL, company_cif VARCHAR(255) DEFAULT NULL, denomination VARCHAR(255) DEFAULT NULL, file_number VARCHAR(100) DEFAULT NULL, num_sessions INT DEFAULT NULL, place LONGTEXT DEFAULT NULL, group_number INT DEFAULT NULL, cost NUMERIC(10, 2) DEFAULT NULL, created_at DATETIME NOT NULL, up
dated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_F316812913AEA17 (announcement_id), UNIQUE INDEX UNIQ_F316812770AF0AD (sessions_announcement_id), INDEX IDX_F31681272D614D0 (type_money_id), INDEX IDX_F316812B03A8386 (created_by_id), INDEX IDX_F316812896DBBDE (updated_by_id), INDEX IDX_F316812C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_group_session (id INT AUTO_INCREMENT NOT NULL, announcement_group_id INT NOT NULL, type_money_id INT DEFAULT NULL, modality_id INT DEFAULT NULL, start_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', finish_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', ur
l LONGTEXT DEFAULT NULL, assistance JSON DEFAULT NULL COMMENT '(DC2Type:json)', session_number INT DEFAULT NULL, student_assistance JSON DEFAULT NULL COMMENT '(DC2Type:json)', entry_margin INT DEFAULT NULL, exit_margin INT DEFAULT NULL, timezone VARCHAR(150) DEFAULT NULL, place VARCHAR(255) DEFAULT NULL, cost NUMERIC(10, 2) DEFAULT NULL, type VARCHAR(100) DEFAULT NULL, INDEX IDX_6FBD43A4851953B4 (announcement_group_id), INDEX IDX_6FBD43A472D614D0 (type_money_id), INDEX IDX_6FBD43A42D6D889B (modality_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE classroomvirtual (id INT AUTO_INCREMENT NOT NULL, announcementgroup_id INT DEFAULT NULL, classroomvirtual_type_id INT DEFAULT NULL, group_session_id INT DEFAULT NULL, roomid BIGINT NOT NULL, roomtype INT NOT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, startsat DATETIME 
NOT NULL, duration INT NOT NULL, session_number INT DEFAULT NULL, state VARCHAR(50) NOT NULL, INDEX IDX_5D831E2F76BD65F0 (announcementgroup_id), INDEX IDX_5D831E2FFE14978B (classroomvirtual_type_id), UNIQUE INDEX UNIQ_5D831E2FB6F28D6D (group_session_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE task_course_group (id INT AUTO_INCREMENT NOT NULL, task_course_id INT DEFAULT NULL, announcement_group_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, num_group INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETI
ME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4CFA6700263604C3 (task_course_id), INDEX IDX_4CFA6700851953B4 (announcement_group_id), INDEX IDX_4CFA6700B03A8386 (created_by_id), INDEX IDX_4CFA6700896DBBDE (updated_by_id), INDEX IDX_4CFA6700C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_notification_group (id INT AUTO_INCREMENT NOT NULL, announcement_notification_id INT DEFAULT NULL, announcement_group_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, num_group INT DEFAULT NULL, created_at DATETIME
 NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_FF834F9A19FA311B (announcement_notification_id), INDEX IDX_FF834F9A851953B4 (announcement_group_id), INDEX IDX_FF834F9AB03A8386 (created_by_id), INDEX IDX_FF834F9A896DBBDE (updated_by_id), INDEX IDX_FF834F9AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_money (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, symbol VARCHAR(20) NOT NULL, country VARCHAR(255) NOT NULL, fractional_unit VARCHAR(255) NOT NULL, code_iso VARCHAR(50) NOT NULL, state TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE chat_channel (id INT AUTO_INCREMENT NOT NULL, server_id INT NOT NULL, parent_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type VARCHAR(45) DEFAULT NULL, name VARCHAR(100) NOT NULL, available_from DATETIME DEFAULT NULL, avai
lable_until DATETIME DEFAULT NULL, entity_id VARCHAR(36) DEFAULT NULL, entity_type VARCHAR(100) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_EEF7422E1844E6B7 (server_id), INDEX IDX_EEF7422E727ACA70 (parent_id), INDEX IDX_EEF7422EB03A8386 (created_by_id), INDEX IDX_EEF7422E896DBBDE (updated_by_id), INDEX IDX_EEF7422EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE integration_group (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(100) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE integration_mapping (id INT AUTO_INCREMENT NOT NULL, integration_group_id INT DEFAULT NULL, entity VARCHAR(255) DEFAULT NULL, identifier VARCHAR(255) DEFAULT NULL, type VARCHAR(20) DEFAULT NULL, mapping JSON NOT NULL COMMENT '(DC2Type:json)', INDEX IDX_24CA96D3EC8E1CAD (integration_group_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_group_session_assistance_files (id INT AUTO_INCREMENT NOT NULL, announcement_group_session_id INT NOT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, INDEX IDX_9362DE17D6B4E314 (announcement_group_session_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_identification (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) DEFAULT NULL, mask LONGTEXT DEFAULT NULL, main TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE files_manager (id INT AUTO_INCREMENT NOT NULL, files_manager_extra_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) NOT NULL, file_size INT NOT NULL, mime_type VARCHAR(
255) DEFAULT NULL, required_role VARCHAR(50) NOT NULL, file_path VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_5BF93AEE74034854 (files_manager_extra_id), INDEX IDX_5BF93AEEB03A8386 (created_by_id), INDEX IDX_5BF93AEE896DBBDE (updated_by_id), INDEX IDX_5BF93AEEC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE setting (id INT AUTO_INCREMENT NOT NULL, setting_group_id INT NOT NULL, code VARCHAR(255) NOT NULL, value LONGTEXT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, sort INT NOT NULL, options JSON NOT NULL COMMENT '(DC2Type:json)', type VARCHAR(60) NOT NULL, INDEX IDX_9F74B89850DDE1BD (setting_group_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE ppt (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, ppt VARCHAR(255) DEFAULT NULL, is_downloadable TINYINT(1) NOT NULL, slides SMALLINT NOT NULL, UNIQUE INDEX UNIQ_D3E77D91579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE classroomvirtual_type (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, state TINYINT(1) NOT NULL, extra JSON DEFAULT NULL COMMENT '(DC2Type:json)', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE chat_server (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(45) NOT NULL, entity_id VARCHAR(40) NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE pages (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, main TINYINT(1) NOT NULL, position INT NOT NULL, created_at DATET
IME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_2074E575B03A8386 (created_by_id), INDEX IDX_2074E575896DBBDE (updated_by_id), INDEX IDX_2074E575C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_identification_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_75F23B762C2AC5D3 (translatable_id), UNIQUE INDEX type_identification_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE lti_line_item (id INT AUTO_INCREMENT NOT NULL, identifier VARCHAR(40) DEFAULT NULL, score_maximum DOUBLE PRECISION NOT NULL, label VARCHAR(255) DEFAULT NULL, resource_id VARCHAR(40) DEFAULT NULL, resource_link_id VARCHAR(40) DEFAULT NULL, tag VARCHAR(100) DEFAULT NULL, start_date_time DATETIME 
DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', end_date_time DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', submission_review JSON DEFAULT NULL COMMENT '(DC2Type:json)', additional_properties JSON NOT NULL COMMENT '(DC2Type:json)', created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_work_department (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_C4A220D6B03A8386 (created_by_id), INDEX IDX_C4A220D6896DBBDE (updated_by_id), INDEX IDX_C4A220D6C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_identification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, type_identification_id INT NOT NULL, identification_value VARCHAR(100) NOT NULL, INDEX IDX_2262AD54A76ED395 (user_id), INDEX IDX_2262AD54F1CF261E (type_identification_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE survey_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_C919A6A2C2AC5D3 (translatable_id), UNIQUE INDEX survey_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_diploma (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, extra LONGTEXT DEFAULT NULL, is_main TINYINT(1) DEFAULT NU
LL, apply_to SMALLINT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_C400E6A2B03A8386 (created_by_id), INDEX IDX_C400E6A2896DBBDE (updated_by_id), INDEX IDX_C400E6A2C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_course_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A7660E6B2C2AC5D3 (translatable_id), UNIQUE INDEX type_course_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_alert_tutor (id INT AUTO_INCREMENT NOT NULL, alert_type_tutor_id INT DEFAULT NULL, announcement_tutor_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, del
eted_at DATETIME DEFAULT NULL, INDEX IDX_DDE31BD5D628A82 (alert_type_tutor_id), INDEX IDX_DDE31BD5BC2EE02 (announcement_tutor_id), INDEX IDX_DDE31BDB03A8386 (created_by_id), INDEX IDX_DDE31BD896DBBDE (updated_by_id), INDEX IDX_DDE31BDC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE extra_data_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_20573AC2C2AC5D3 (translatable_id), UNIQUE INDEX extra_data_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE chapter_type_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_DEBCD0932C2AC5D3 (translatable_id), UNIQUE INDEX chapter_type_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_modality (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) DEFAULT NULL, is_active TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE cron_job_timeout (id INT AUTO_INCREMENT NOT NULL, cron_job_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, command VARCHAR(255) NOT NULL, timeout INT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL
, deleted_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_427242B48ECAEAD4 (command), UNIQUE INDEX UNIQ_427242B479099ED8 (cron_job_id), INDEX IDX_427242B4B03A8386 (created_by_id), INDEX IDX_427242B4896DBBDE (updated_by_id), INDEX IDX_427242B4C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE pages_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_7D0CA9D12C2AC5D3 (translatable_id), UNIQUE INDEX pages_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE setting_group (id INT AUTO_INCREMENT NOT NULL, title VARCHAR(255) NOT NULL, sort INT NOT NULL, code VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE lti_line_item_result (id INT AUTO_INCREMENT NOT NULL, identifier LONGTEXT NOT NULL, score_of LONGTEXT NOT NULL, user_id VARCHAR(40) NOT NULL, result_score DOUBLE PRECISION NOT NULL, result_maximum DOUBLE PRECISION NOT NULL, scoring_user_id VARCHAR(40) DEFAULT NULL, comment LONGTEXT DEFAULT NULL, additional_properties JSON NOT NULL COMMENT '(DC2Type:json)', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE section_default_front_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_B2D0C7942C2AC5D3 (translatable_id), UNIQUE INDEX section_default_front_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_company (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, cif VARCHAR(255) DEFAULT NULL, profile VARCHAR(255) DEFAULT N
ULL, code VARCHAR(255) DEFAULT NULL, external TINYINT(1) DEFAULT 1 NOT NULL, active TINYINT(1) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_17B21745B03A8386 (created_by_id), INDEX IDX_17B21745896DBBDE (updated_by_id), INDEX IDX_17B21745C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE zip_file_task (id INT AUTO_INCREMENT NOT NULL, files_manager_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type VARCHAR(20) NOT NULL, entity_id VARCHAR(10) DEFAULT NULL, params JSON NOT NULL COMMENT '(DC2Type:json)', task VA
RCHAR(255) NOT NULL, status SMALLINT NOT NULL, filename LONGTEXT DEFAULT NULL, original_name LONGTEXT DEFAULT NULL, started_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', finished_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', available_at DATETIME DEFAULT NULL, created_a
t DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_B192226A24F5AC00 (files_manager_id), INDEX IDX_B192226AB03A8386 (created_by_id), INDEX IDX_B192226A896DBBDE (updated_by_id), INDEX IDX_B192226AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_modality_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_ECD0153C2C2AC5D3 (translatable_id), UNIQUE INDEX announcement_modality_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE lti_chapter (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, lti_tool_id INT DEFAULT NULL, identifier VARCHAR(255) NOT NULL, lti_tool_identifier_id VARCHAR(36) DEFAULT NULL, UNIQUE INDEX UNIQ_D6C02C34579F4768 (chapter_id), INDEX IDX_D6C02C34A03ABBD6 (lti_tool_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE files_manager_extra (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(50) NOT NULL, entity_id VARCHAR(40) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_criteria_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_B9B4419B2C2AC5D3 (translatable_id), UNIQUE INDEX announcement_criteria_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE course_category_type_course (course_category_id INT NOT NULL, type_course_id INT NOT NULL, INDEX IDX_5CE7DCD6628AD36 (course_category_id), INDEX IDX_5CE7DCDEDDA8882 (type_course_id), PRIMARY KEY(course_category_id, type_course_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_course_alerts (id INT AUTO_INCREMENT NOT NULL, type_course_id INT NOT NULL, alert_type_tutor_id INT NOT NULL, INDEX IDX_29AFB613EDDA8882 (type_course_id), INDEX IDX_29AFB6135D628A82 (alert_type_tutor_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_notification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, is_active TINYINT(1) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_3F980AC8A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE lti_tool (id INT AUTO_INCREMENT NOT NULL, tools_name VARCHAR(50) NOT NULL, name VARCHAR(45) NOT NULL, audience VARCHAR(200) NOT NULL, oidc_authentication_url VARCHAR(200) NOT NULL, launch_url VARCHAR(200) NOT NULL, deep_linking_url VARCHAR(200) NOT NULL, client_id VARCHAR(45) NOT NULL, deployments_ids VARCHAR(45) NOT NULL, platform_jwks_url VARCHAR(200) NOT NULL, tool_jwks_url VARCHAR(200) NOT NULL, platform VARCHAR(200) NOT NULL, identifier VARCHAR(45) NOT NULL, platform_key_chain VARCHAR(45) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;  
CREATE TABLE email_notification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, itinerary_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, forum_post_id INT DEFAULT NULL, sent TINYINT(1) DEFAULT NULL, type VARCHAR(50) DEFAULT NULL, title VARCHAR(100) DEFAULT NULL, message VARCHAR(250) DEFAULT N
ULL, attributes JSON DEFAULT NULL COMMENT '(DC2Type:json)', translation_text VARCHAR(255) DEFAULT NULL, translation_title VARCHAR(255) DEFAULT NULL, extra JSON DEFAULT NULL COMMENT '(DC2Type:json)', created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_EA479099A76ED395 (user_id), INDEX IDX_EA47909915F737B2 (itinerary_id), INDEX IDX_EA479099913AEA17 (announcement_id), INDEX IDX_EA479099BA454E5D (forum_post_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE catalog_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A5DD64C12C2AC5D3 (translatable_id), UNIQUE INDEX catalog_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE generic_token (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, token VARCHAR(255) NOT NULL, type VARCHAR(100) NOT NULL, entity_id INT NOT NULL, extra JSON DEFAULT NULL COMMENT '(DC2Type:json)', valid_from DATETIME D
EFAULT NULL COMMENT '(DC2Type:datetime_immutable)', valid_until DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', revoked TINYINT(1) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_360B1929B03A8386 (created_by_id), INDEX IDX_360B1929896DBBDE (updated_by_id), INDEX IDX_360B1929C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE classroom_virtual_result (id INT AUTO_INCREMENT NOT NULL, classroom_virtual_id INT DEFAULT NULL, result LONGTEXT NOT NULL, created_at DATETIME NOT NULL, update_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_B6A56DE127076DC2 (classroom_virtual_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE meetingzoom_token (id INT AUTO_INCREMENT NOT NULL, token LONGTEXT NOT NULL, created_at DATETIME NOT NULL, user_id INT DEFAULT NULL, estado VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_study_level (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_6C8B5566B03A8386 (created_by_id), INDEX IDX_6C8B5566896DBBDE (updated_by_id), INDEX IDX_6C8B5566C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE chat_channel_user (id INT AUTO_INCREMENT NOT NULL, channel_id INT NOT NULL, user_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', INDEX IDX_EF2C1CB672F5A1AA (channel_id), INDEX IDX_EF2C1CB6A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_course_announcement_step_creation_trans (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_7100AE352C2AC5D3 (translatable_id), UNIQUE INDEX type_course_announcement_step_creation_trans_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_course_announcement_step_configuration (id INT AUTO_INCREMENT NOT NULL, announcement_configuration_type_id INT NOT NULL, type_course_announcement_step_creation_id INT NOT NULL, active TINYINT(1) DEFAULT NULL, INDEX IDX_82556DB275D5DC16 (announcement_configuration_type_id), INDEX IDX_82556DB26DFFE627 (type_course_announcement_step_creation_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE section_default_front (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, is_active TINYINT(1) DEFAULT NULL, id_section INT DEFAULT NULL, is_open_course TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE catalog (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, component VARCHAR(255) DEFAULT NULL, route VARCHAR(255) DEFAULT NULL, relation VARCHAR(255) NOT NULL, service VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_money_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A87D08C72C2AC5D3 (translatable_id), UNIQUE INDEX type_money_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_course (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, active TINYINT(1) NOT NULL, code VARCHAR(50) DEFAULT NULL, denomination VARCHAR(255) DEFAUL
T NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_35490481B03A8386 (created_by_id), INDEX IDX_35490481896DBBDE (updated_by_id), INDEX IDX_35490481C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_manager (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, manager_id INT NOT NULL, INDEX IDX_6857873913AEA17 (announcement_id), INDEX IDX_6857873783E3463 (manager_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_user_digital_signature (id INT AUTO_INCREMENT NOT NULL, announcement_user_id INT DEFAULT NULL, announcement_group_session_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, INDEX IDX_41FE20DA5BCF8E5F (announcement_user_id), INDEX IDX_41FE20DAD6B4E314 (announcement_group_session_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_configuration (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, configuration_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIM
E DEFAULT NULL, INDEX IDX_9C1B67AB913AEA17 (announcement_id), INDEX IDX_9C1B67AB73F32DD8 (configuration_id), INDEX IDX_9C1B67ABB03A8386 (created_by_id), INDEX IDX_9C1B67AB896DBBDE (updated_by_id), INDEX IDX_9C1B67ABC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_work_center (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4212E493B03A8386 (created_by_id), INDEX IDX_4212E493896DBBDE (updated_by_id), INDEX IDX_4212E493C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_professional_category (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_55CE8E02B03A8386 (created_by_id), INDEX IDX_55CE8E02896DBBDE (updated_by_id), INDEX IDX_55CE8E02C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE classroomvirtual_user (id INT AUTO_INCREMENT NOT NULL, announcementuser_id INT DEFAULT NULL, announcementtutor_id INT DEFAULT NULL, classroomvirtual_id INT DEFAULT NULL, classroomvirtualuser_url LONGTEXT DEFAULT NULL, INDEX IDX_CF87B8EB4688824D (announcementuser_id), INDEX IDX_CF87B8EBA866D846 (announcementtutor_id), INDEX IDX_CF87B8EBF5A029F5 (classroomvirtual_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE integration_users_data (id INT AUTO_INCREMENT NOT NULL, params JSON NOT NULL COMMENT '(DC2Type:json)', created_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', updated_at DATETIME DEFAULT NULL, completed TINYINT(1) NOT NULL, running TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_inspector_access (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, token_id INT NOT NULL, user VARCHAR(45) NOT NULL, password VARCHAR(255) NOT NULL, roles JSON NOT NULL COMMENT '(DC2Type:json)', UNIQUE INDEX UNIQ_628C140B913AEA17 (announcement_id), UNIQUE INDEX UNIQ_628C140B41DEE7B9 (token_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE api_key_request (id INT AUTO_INCREMENT NOT NULL, api_key_user_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', ip VARCHAR(255) NOT NULL, endpoint VARCHAR(255) NOT NULL, INDEX IDX_3336212FAFCF7D25 (api_key_user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE api_key_user (id INT AUTO_INCREMENT NOT NULL, api_key VARCHAR(255) NOT NULL, username VARCHAR(255) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_configuration_type (id INT AUTO_INCREMENT NOT NULL, configuration_client_announcement_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, image VARCHAR(25
5) DEFAULT NULL, active TINYINT(1) NOT NULL, code VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_BD7ACF885E9A4305 (configuration_client_announcement_id), INDEX IDX_BD7ACF88B03A8386 (created_by_id), INDEX IDX_BD7ACF88896DBBDE (updated_by_id), INDEX IDX_BD7ACF88C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE lti_line_item_score (id INT AUTO_INCREMENT NOT NULL, user_id VARCHAR(40) NOT NULL, activity_progress VARCHAR(100) NOT NULL, grading_progress VARCHAR(100) NOT NULL, timestamp VARCHAR(40) NOT NULL, score_given DOUBLE PRECISION NOT NULL, score_maximum DOUBLE PRECISION NOT NULL, comment LONGTEXT DE
FAULT NULL, scoring_user_id LONGTEXT DEFAULT NULL, line_item_identifier VARCHAR(40) NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', additional_properties JSON NOT NULL COMMENT '(DC2Type:json)', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE itinerary_tags (id INT AUTO_INCREMENT NOT NULL, filter_id INT NOT NULL, itinerary_id INT NOT NULL, INDEX IDX_689FFE72D395B25E (filter_id), INDEX IDX_689FFE7215F737B2 (itinerary_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_step_creation (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) DEFAULT NULL, position INT DEFAULT NULL, extra JSON DEFAULT NULL COMMENT '(DC2Type:json)', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE configuration_client_announcement (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, extra JSON DEFAULT NULL COMMENT '(DC2
Type:json)', code VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_71AECC53B03A8386 (created_by_id), INDEX IDX_71AECC53896DBBDE (updated_by_id), INDEX IDX_71AECC53C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE itinerary_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_D12AC99A2C2AC5D3 (translatable_id), UNIQUE INDEX itinerary_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE translations_admin (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE nps_question_detail_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, value LONGTEXT NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_BFEB50BF2C2AC5D3 (translatable_id), UNIQUE INDEX nps_question_detail_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_criteria (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, extra JSON DEFAULT NULL COMMENT '(DC2Type:json)',
 created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_CD521172B03A8386 (created_by_id), INDEX IDX_CD521172896DBBDE (updated_by_id), INDEX IDX_CD521172C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE chat_message (id INT AUTO_INCREMENT NOT NULL, channel_id INT NOT NULL, user_id INT NOT NULL, reply_to_id INT DEFAULT NULL, message LONGTEXT NOT NULL, seen TINYINT(1) NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', updated_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', INDEX IDX_FAB3FC1672F5A1AA (channel_id), INDEX IDX_FAB3FC16A76ED395 (user_id), INDEX IDX_FAB3FC16FFDF7169 (reply_to_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE alert_type_tutor (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_1604A96FB03A8386 (created_by_id), INDEX IDX_1604A96F896DBBDE (updated_by_id), INDEX IDX_1604A96FC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_history_download_diploma (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DAT
ETIME DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_8F67717E591CC992 (course_id), INDEX IDX_8F67717E913AEA17 (announcement_id), INDEX IDX_8F67717EB03A8386 (created_by_id), INDEX IDX_8F67717E896DBBDE (updated_by_id), INDEX IDX_8F67717EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE course_stat (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, valoration_nps NUMERIC(10, 2) DEFAULT NULL, count_valoration_nps NUMERIC(10, 2) DEFAULT NULL, INDEX IDX_E81A2227591CC992 (course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE alert_type_tutor_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_EC131BA42C2AC5D3 (translatable_id), UNIQUE INDEX alert_type_tutor_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_didatic_guide (id INT AUTO_INCREMENT NOT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, filename VA
RCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, UNIQUE INDEX UNIQ_BAC51D60913AEA17 (announcement_id), INDEX IDX_BAC51D60B03A8386 (created_by_id), INDEX IDX_BAC51D60896DBBDE (updated_by_id), INDEX IDX_BAC51D60C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE extra_data (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_temporalization (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, started_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', finis
hed_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', minimum_time INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_CEC55850579F4768 (chapter_id), INDEX IDX_CEC55850913AEA17 (announcement_id), INDEX IDX_CEC55850B03A8386 (created_by_id), INDEX IDX_CEC55850896DBBDE (updated_by_id), INDEX IDX_CEC55850C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE user_fields_fundae (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, user_company_id INT DEFAULT NULL, user_professional_category_id INT DEFAULT NULL, user_work_center_id INT DEFAULT NULL, user_work_department_id INT DEFAULT NULL, user_study_level_id INT DEFAULT NULL, cv_files_manager_id I
NT DEFAULT NULL, social_security_number VARCHAR(255) DEFAULT NULL, gender VARCHAR(50) DEFAULT NULL, email_work VARCHAR(255) DEFAULT NULL, birthdate DATE DEFAULT NULL, incapacity TINYINT(1) DEFAULT NULL, victim_of_terrorism TINYINT(1) DEFAULT NULL, gender_violence TINYINT(1) DEFAULT NULL, dni VARCHAR(50) DEF
AULT NULL, contribution_account VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, telephone VARCHAR(30) DEFAULT NULL, UNIQUE INDEX UNIQ_DC0416CCA76ED395 (user_id), INDEX IDX_DC0416CC30FCDC3A (user_company_id), INDEX IDX_DC0416CCBC194B79 (user_professional_category_id), INDEX IDX_DC0416CC50BF847B (user_work_center_id), INDEX IDX_DC0416CC42196A10 (user_work_department_id), INDEX IDX_DC0416CC97BD8177 (user_study_level_id), INDEX IDX_DC0416CC911DD53E (cv_files_manager_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;     
CREATE TABLE chat_message_report (id INT AUTO_INCREMENT NOT NULL, message_id INT NOT NULL, user_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', active TINYINT(1) NOT NULL, INDEX IDX_E5FE6496537A1329 (message_id), INDEX IDX_E5FE6496A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_configuration_type_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_E60E04A22C2AC5D3 (translatable_id), UNIQUE INDEX announcement_configuration_type_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_notification (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, text LONGTEXT NOT NULL, state INT NOT NULL, is_active TINYINT(1) NOT NULL, send_at DATETIME NOT NULL COMMENT '
(DC2Type:datetime_immutable)', created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4525C829913AEA17 (announcement_id), INDEX IDX_4525C829B03A8386 (created_by_id), INDEX IDX_4525C829896DBBDE (updated_by_id), INDEX IDX_4525C829C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_aproved_criteria (id INT AUTO_INCREMENT NOT NULL, announcement_id INT DEFAULT NULL, announcement_criteria_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, value NUMERIC(10, 0) NOT NULL, extra LONGTEXT DEFAULT NULL,
 created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_7324EB3E913AEA17 (announcement_id), INDEX IDX_7324EB3EE7886A0B (announcement_criteria_id), INDEX IDX_7324EB3EB03A8386 (created_by_id), INDEX IDX_7324EB3E896DBBDE (updated_by_id), INDEX IDX_7324EB3EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE type_course_announcement_step_creation (id INT AUTO_INCREMENT NOT NULL, type_course_id INT NOT NULL, announcement_step_creation_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, position INT DEFAULT NULL, is_required TINYINT(1) DEFAULT NULL, extra JSON DEFAULT NULL COMMENT '(DC2Type:json)', active TINYINT(1) DEFAULT NULL, INDEX IDX_86190621EDDA8882 (type_course_id), INDEX IDX_86190621556ED2C3 (announcement_step_creation_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE chat_message_like (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, chat_message_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', active TINYINT(1) NOT NULL, INDEX IDX_FB1DB2EBA76ED395 (user_id), INDEX IDX_FB1DB2EB948B568F (chat_message_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE announcement_tutor_connection (id INT AUTO_INCREMENT NOT NULL, announcement_tutor_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, ip VARCHAR(100) NOT NULL, time INT NOT NULL, extra LONGTEXT DEFAULT NULL, created_at DATETIME NO
T NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_817BA0395BC2EE02 (announcement_tutor_id), INDEX IDX_817BA039B03A8386 (created_by_id), INDEX IDX_817BA039896DBBDE (updated_by_id), INDEX IDX_817BA039C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
CREATE TABLE translations_admin_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_9EBEB10D2C2AC5D3 (translatable_id), UNIQUE INDEX translations_admin_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB;
ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812770AF0AD FOREIGN KEY (sessions_announcement_id) REFERENCES sessions_announcement (id);
ALTER TABLE announcement_group ADD CONSTRAINT FK_F31681272D614D0 FOREIGN KEY (type_money_id) REFERENCES type_money (id);
ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE announcement_group_session ADD CONSTRAINT FK_6FBD43A4851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id);
ALTER TABLE announcement_group_session ADD CONSTRAINT FK_6FBD43A472D614D0 FOREIGN KEY (type_money_id) REFERENCES type_money (id);
ALTER TABLE announcement_group_session ADD CONSTRAINT FK_6FBD43A42D6D889B FOREIGN KEY (modality_id) REFERENCES announcement_modality (id);
ALTER TABLE classroomvirtual ADD CONSTRAINT FK_5D831E2F76BD65F0 FOREIGN KEY (announcementgroup_id) REFERENCES announcement_group (id);
ALTER TABLE classroomvirtual ADD CONSTRAINT FK_5D831E2FFE14978B FOREIGN KEY (classroomvirtual_type_id) REFERENCES classroomvirtual_type (id);
ALTER TABLE classroomvirtual ADD CONSTRAINT FK_5D831E2FB6F28D6D FOREIGN KEY (group_session_id) REFERENCES announcement_group_session (id);
ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700263604C3 FOREIGN KEY (task_course_id) REFERENCES task_course (id);
ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id);
ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9A19FA311B FOREIGN KEY (announcement_notification_id) REFERENCES announcement_notification (id);
ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9A851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id);
ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422E1844E6B7 FOREIGN KEY (server_id) REFERENCES chat_server (id);
ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422E727ACA70 FOREIGN KEY (parent_id) REFERENCES chat_channel (id);
ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE integration_mapping ADD CONSTRAINT FK_24CA96D3EC8E1CAD FOREIGN KEY (integration_group_id) REFERENCES integration_group (id);
ALTER TABLE announcement_group_session_assistance_files ADD CONSTRAINT FK_9362DE17D6B4E314 FOREIGN KEY (announcement_group_session_id) REFERENCES announcement_group_session (id);
ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEE74034854 FOREIGN KEY (files_manager_extra_id) REFERENCES files_manager_extra (id);
ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEEB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEE896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEEC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE setting ADD CONSTRAINT FK_9F74B89850DDE1BD FOREIGN KEY (setting_group_id) REFERENCES setting_group (id);
ALTER TABLE ppt ADD CONSTRAINT FK_D3E77D91579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id);
ALTER TABLE pages ADD CONSTRAINT FK_2074E575B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE pages ADD CONSTRAINT FK_2074E575896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE pages ADD CONSTRAINT FK_2074E575C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE type_identification_translation ADD CONSTRAINT FK_75F23B762C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_identification (id) ON DELETE CASCADE;
ALTER TABLE user_work_department ADD CONSTRAINT FK_C4A220D6B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_work_department ADD CONSTRAINT FK_C4A220D6896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_work_department ADD CONSTRAINT FK_C4A220D6C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE user_identification ADD CONSTRAINT FK_2262AD54A76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE user_identification ADD CONSTRAINT FK_2262AD54F1CF261E FOREIGN KEY (type_identification_id) REFERENCES type_identification (id);
ALTER TABLE survey_translation ADD CONSTRAINT FK_C919A6A2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES survey (id) ON DELETE CASCADE;
ALTER TABLE type_diploma ADD CONSTRAINT FK_C400E6A2B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE type_diploma ADD CONSTRAINT FK_C400E6A2896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE type_diploma ADD CONSTRAINT FK_C400E6A2C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE type_course_translation ADD CONSTRAINT FK_A7660E6B2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_course (id) ON DELETE CASCADE;
ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BD5D628A82 FOREIGN KEY (alert_type_tutor_id) REFERENCES alert_type_tutor (id);
ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BD5BC2EE02 FOREIGN KEY (announcement_tutor_id) REFERENCES announcement_tutor (id);
ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BDB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BD896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BDC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE extra_data_translation ADD CONSTRAINT FK_20573AC2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES extra_data (id) ON DELETE CASCADE;
ALTER TABLE chapter_type_translation ADD CONSTRAINT FK_DEBCD0932C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES chapter_type (id) ON DELETE CASCADE;
ALTER TABLE cron_job_timeout ADD CONSTRAINT FK_427242B479099ED8 FOREIGN KEY (cron_job_id) REFERENCES cron_job (id);
ALTER TABLE cron_job_timeout ADD CONSTRAINT FK_427242B4B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE cron_job_timeout ADD CONSTRAINT FK_427242B4896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE cron_job_timeout ADD CONSTRAINT FK_427242B4C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE pages_translation ADD CONSTRAINT FK_7D0CA9D12C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES pages (id) ON DELETE CASCADE;
ALTER TABLE section_default_front_translation ADD CONSTRAINT FK_B2D0C7942C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES section_default_front (id) ON DELETE CASCADE;
ALTER TABLE user_company ADD CONSTRAINT FK_17B21745B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_company ADD CONSTRAINT FK_17B21745896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_company ADD CONSTRAINT FK_17B21745C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226A24F5AC00 FOREIGN KEY (files_manager_id) REFERENCES files_manager (id);
ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE announcement_modality_translation ADD CONSTRAINT FK_ECD0153C2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES announcement_modality (id) ON DELETE CASCADE;
ALTER TABLE lti_chapter ADD CONSTRAINT FK_D6C02C34579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id);
ALTER TABLE lti_chapter ADD CONSTRAINT FK_D6C02C34A03ABBD6 FOREIGN KEY (lti_tool_id) REFERENCES lti_tool (id);
ALTER TABLE announcement_criteria_translation ADD CONSTRAINT FK_B9B4419B2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES announcement_criteria (id) ON DELETE CASCADE;
ALTER TABLE course_category_type_course ADD CONSTRAINT FK_5CE7DCD6628AD36 FOREIGN KEY (course_category_id) REFERENCES course_category (id) ON DELETE CASCADE;
ALTER TABLE course_category_type_course ADD CONSTRAINT FK_5CE7DCDEDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id) ON DELETE CASCADE;
ALTER TABLE type_course_alerts ADD CONSTRAINT FK_29AFB613EDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id);
ALTER TABLE type_course_alerts ADD CONSTRAINT FK_29AFB6135D628A82 FOREIGN KEY (alert_type_tutor_id) REFERENCES alert_type_tutor (id);
ALTER TABLE user_notification ADD CONSTRAINT FK_3F980AC8A76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE email_notification ADD CONSTRAINT FK_EA479099A76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE email_notification ADD CONSTRAINT FK_EA47909915F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id);
ALTER TABLE email_notification ADD CONSTRAINT FK_EA479099913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE email_notification ADD CONSTRAINT FK_EA479099BA454E5D FOREIGN KEY (forum_post_id) REFERENCES forum_post (id);
ALTER TABLE catalog_translation ADD CONSTRAINT FK_A5DD64C12C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES catalog (id) ON DELETE CASCADE;
ALTER TABLE generic_token ADD CONSTRAINT FK_360B1929B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE generic_token ADD CONSTRAINT FK_360B1929896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE generic_token ADD CONSTRAINT FK_360B1929C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE classroom_virtual_result ADD CONSTRAINT FK_B6A56DE127076DC2 FOREIGN KEY (classroom_virtual_id) REFERENCES classroomvirtual (id);
ALTER TABLE user_study_level ADD CONSTRAINT FK_6C8B5566B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_study_level ADD CONSTRAINT FK_6C8B5566896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_study_level ADD CONSTRAINT FK_6C8B5566C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE chat_channel_user ADD CONSTRAINT FK_EF2C1CB672F5A1AA FOREIGN KEY (channel_id) REFERENCES chat_channel (id);
ALTER TABLE chat_channel_user ADD CONSTRAINT FK_EF2C1CB6A76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE type_course_announcement_step_creation_trans ADD CONSTRAINT FK_7100AE352C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_course_announcement_step_creation (id) ON DELETE CASCADE;
ALTER TABLE type_course_announcement_step_configuration ADD CONSTRAINT FK_82556DB275D5DC16 FOREIGN KEY (announcement_configuration_type_id) REFERENCES announcement_configuration_type (id);
ALTER TABLE type_course_announcement_step_configuration ADD CONSTRAINT FK_82556DB26DFFE627 FOREIGN KEY (type_course_announcement_step_creation_id) REFERENCES type_course_announcement_step_creation (id);
ALTER TABLE type_money_translation ADD CONSTRAINT FK_A87D08C72C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_money (id) ON DELETE CASCADE;
ALTER TABLE type_course ADD CONSTRAINT FK_35490481B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE type_course ADD CONSTRAINT FK_35490481896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE type_course ADD CONSTRAINT FK_35490481C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE announcement_manager ADD CONSTRAINT FK_6857873913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_manager ADD CONSTRAINT FK_6857873783E3463 FOREIGN KEY (manager_id) REFERENCES user (id);
ALTER TABLE announcement_user_digital_signature ADD CONSTRAINT FK_41FE20DA5BCF8E5F FOREIGN KEY (announcement_user_id) REFERENCES announcement_user (id);
ALTER TABLE announcement_user_digital_signature ADD CONSTRAINT FK_41FE20DAD6B4E314 FOREIGN KEY (announcement_group_session_id) REFERENCES announcement_group_session (id);
ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67AB913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67AB73F32DD8 FOREIGN KEY (configuration_id) REFERENCES announcement_configuration_type (id);
ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67ABB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67AB896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67ABC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE user_work_center ADD CONSTRAINT FK_4212E493B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_work_center ADD CONSTRAINT FK_4212E493896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_work_center ADD CONSTRAINT FK_4212E493C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE user_professional_category ADD CONSTRAINT FK_55CE8E02B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_professional_category ADD CONSTRAINT FK_55CE8E02896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_professional_category ADD CONSTRAINT FK_55CE8E02C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE classroomvirtual_user ADD CONSTRAINT FK_CF87B8EB4688824D FOREIGN KEY (announcementuser_id) REFERENCES announcement_user (id);
ALTER TABLE classroomvirtual_user ADD CONSTRAINT FK_CF87B8EBA866D846 FOREIGN KEY (announcementtutor_id) REFERENCES announcement_tutor (id);
ALTER TABLE classroomvirtual_user ADD CONSTRAINT FK_CF87B8EBF5A029F5 FOREIGN KEY (classroomvirtual_id) REFERENCES classroomvirtual (id);
ALTER TABLE announcement_inspector_access ADD CONSTRAINT FK_628C140B913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_inspector_access ADD CONSTRAINT FK_628C140B41DEE7B9 FOREIGN KEY (token_id) REFERENCES user_token (id);
ALTER TABLE api_key_request ADD CONSTRAINT FK_3336212FAFCF7D25 FOREIGN KEY (api_key_user_id) REFERENCES api_key_user (id);
ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF885E9A4305 FOREIGN KEY (configuration_client_announcement_id) REFERENCES configuration_client_announcement (id);
ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF88B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF88896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF88C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE itinerary_tags ADD CONSTRAINT FK_689FFE72D395B25E FOREIGN KEY (filter_id) REFERENCES filter (id);
ALTER TABLE itinerary_tags ADD CONSTRAINT FK_689FFE7215F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id);
ALTER TABLE configuration_client_announcement ADD CONSTRAINT FK_71AECC53B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE configuration_client_announcement ADD CONSTRAINT FK_71AECC53896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE configuration_client_announcement ADD CONSTRAINT FK_71AECC53C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE itinerary_translation ADD CONSTRAINT FK_D12AC99A2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES itinerary (id) ON DELETE CASCADE;
ALTER TABLE nps_question_detail_translation ADD CONSTRAINT FK_BFEB50BF2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES nps_question_detail (id) ON DELETE CASCADE;
ALTER TABLE announcement_criteria ADD CONSTRAINT FK_CD521172B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_criteria ADD CONSTRAINT FK_CD521172896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_criteria ADD CONSTRAINT FK_CD521172C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE chat_message ADD CONSTRAINT FK_FAB3FC1672F5A1AA FOREIGN KEY (channel_id) REFERENCES chat_channel (id);
ALTER TABLE chat_message ADD CONSTRAINT FK_FAB3FC16A76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE chat_message ADD CONSTRAINT FK_FAB3FC16FFDF7169 FOREIGN KEY (reply_to_id) REFERENCES chat_message (id);
ALTER TABLE alert_type_tutor ADD CONSTRAINT FK_1604A96FB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE alert_type_tutor ADD CONSTRAINT FK_1604A96F896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE alert_type_tutor ADD CONSTRAINT FK_1604A96FC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717E591CC992 FOREIGN KEY (course_id) REFERENCES course (id);
ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717E913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE course_stat ADD CONSTRAINT FK_E81A2227591CC992 FOREIGN KEY (course_id) REFERENCES course (id);
ALTER TABLE alert_type_tutor_translation ADD CONSTRAINT FK_EC131BA42C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES alert_type_tutor (id) ON DELETE CASCADE;
ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id);
ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CCA76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC30FCDC3A FOREIGN KEY (user_company_id) REFERENCES user_company (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CCBC194B79 FOREIGN KEY (user_professional_category_id) REFERENCES user_professional_category (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC50BF847B FOREIGN KEY (user_work_center_id) REFERENCES user_work_center (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC42196A10 FOREIGN KEY (user_work_department_id) REFERENCES user_work_department (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC97BD8177 FOREIGN KEY (user_study_level_id) REFERENCES user_study_level (id);
ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC911DD53E FOREIGN KEY (cv_files_manager_id) REFERENCES files_manager (id);
ALTER TABLE chat_message_report ADD CONSTRAINT FK_E5FE6496537A1329 FOREIGN KEY (message_id) REFERENCES chat_message (id);
ALTER TABLE chat_message_report ADD CONSTRAINT FK_E5FE6496A76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE announcement_configuration_type_translation ADD CONSTRAINT FK_E60E04A22C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES announcement_configuration_type (id) ON DELETE CASCADE;
ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3E913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3EE7886A0B FOREIGN KEY (announcement_criteria_id) REFERENCES announcement_criteria (id);
ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE type_course_announcement_step_creation ADD CONSTRAINT FK_86190621EDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id);
ALTER TABLE type_course_announcement_step_creation ADD CONSTRAINT FK_86190621556ED2C3 FOREIGN KEY (announcement_step_creation_id) REFERENCES announcement_step_creation (id);
ALTER TABLE chat_message_like ADD CONSTRAINT FK_FB1DB2EBA76ED395 FOREIGN KEY (user_id) REFERENCES user (id);
ALTER TABLE chat_message_like ADD CONSTRAINT FK_FB1DB2EB948B568F FOREIGN KEY (chat_message_id) REFERENCES chat_message (id);
ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA0395BC2EE02 FOREIGN KEY (announcement_tutor_id) REFERENCES announcement_tutor (id);
ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA039B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA039896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA039C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
ALTER TABLE translations_admin_translation ADD CONSTRAINT FK_9EBEB10D2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES translations_admin (id) ON DELETE CASCADE;
ALTER TABLE announcement ADD type_diploma_id INT DEFAULT NULL, ADD code VARCHAR(100) DEFAULT NULL, ADD users_per_group INT NOT NULL, ADD objective_and_contents LONGTEXT DEFAULT NULL, ADD action_type VARCHAR(100) DEFAULT NULL, ADD action_code VARCHAR(100) DEFAULT NULL, ADD denomination VARCHAR(255) DEFAULT N
ULL, ADD contact_person_email VARCHAR(255) DEFAULT NULL, ADD contact_person_telephone VARCHAR(20) DEFAULT NULL, ADD notified_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', ADD is_confirmation_required_diploma TINYINT(1) DEFAULT NULL, ADD extra JSON DEFAULT NULL COMMENT '(DC2Type:json)', AD
D timezone VARCHAR(150) DEFAULT NULL, ADD status VARCHAR(40) DEFAULT NULL, DROP s_group, DROP action_denomination, DROP modality, DROP place_of_instruction, DROP collaboration_type, DROP provider, DROP provider_cif, DROP hotel, DROP society, DROP subsidized_group, DROP department, DROP client_contact_person
, DROP af_type, DROP formation_level, DROP number_of_participants_af, DROP number_of_groups, DROP directed_to, DROP selection_criteria, DROP telephone, DROP trainer, DROP trainer_center, DROP trainer_center_cif, CHANGE total_hours total_hours NUMERIC(10, 2) DEFAULT NULL, CHANGE general_information general_information LONGTEXT DEFAULT NULL;
ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91CB4991890 FOREIGN KEY (type_diploma_id) REFERENCES type_diploma (id);
CREATE INDEX IDX_4DB9D91CB4991890 ON announcement (type_diploma_id);
ALTER TABLE announcement_user ADD announcement_group_id INT DEFAULT NULL, ADD user_company_id INT DEFAULT NULL, ADD user_professional_category_id INT DEFAULT NULL, ADD user_work_center_id INT DEFAULT NULL, ADD user_work_department_id INT DEFAULT NULL, ADD user_study_level_id INT DEFAULT NULL, ADD is_aproved
 TINYINT(1) DEFAULT NULL, ADD date_approved DATETIME DEFAULT NULL, ADD is_download_diploma TINYINT(1) DEFAULT NULL, ADD is_read_didactic_guide TINYINT(1) DEFAULT NULL, ADD date_read_didactic_guide DATETIME DEFAULT NULL, ADD is_download_didactic_guide TINYINT(1) DEFAULT NULL, ADD date_download_didactic_guide DATETIME DEFAULT NULL, ADD valued_course_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', ADD is_confirmation_assistance TINYINT(1) DEFAULT NULL, ADD date_confirmation_assistance DATETIME DEFAULT NULL, ADD external TINYINT(1) NOT NULL;
ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE15851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id);
ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1530FCDC3A FOREIGN KEY (user_company_id) REFERENCES user_company (id);
ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE15BC194B79 FOREIGN KEY (user_professional_category_id) REFERENCES user_professional_category (id);
ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1550BF847B FOREIGN KEY (user_work_center_id) REFERENCES user_work_center (id);
ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1542196A10 FOREIGN KEY (user_work_department_id) REFERENCES user_work_department (id);
ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1597BD8177 FOREIGN KEY (user_study_level_id) REFERENCES user_study_level (id);
CREATE INDEX IDX_A1A2DE15851953B4 ON announcement_user (announcement_group_id);
CREATE INDEX IDX_A1A2DE1530FCDC3A ON announcement_user (user_company_id);
CREATE INDEX IDX_A1A2DE15BC194B79 ON announcement_user (user_professional_category_id);
CREATE INDEX IDX_A1A2DE1550BF847B ON announcement_user (user_work_center_id);
CREATE INDEX IDX_A1A2DE1542196A10 ON announcement_user (user_work_department_id);
CREATE INDEX IDX_A1A2DE1597BD8177 ON announcement_user (user_study_level_id);
ALTER TABLE announcement_tutor ADD announcement_group_id INT DEFAULT NULL, ADD cv_files_manager_id INT DEFAULT NULL, ADD user_company_id INT DEFAULT NULL, ADD dni VARCHAR(100) DEFAULT NULL, ADD email VARCHAR(255) DEFAULT NULL, ADD telephone VARCHAR(20) DEFAULT NULL, ADD tutoring_time LONGTEXT DEFAULT NULL, ADD name VARCHAR(255) DEFAULT NULL, ADD filename VARCHAR(255) DEFAULT NULL, ADD original_name VARCHAR(255) DEFAULT NULL, ADD file_size INT DEFAULT NULL, ADD mime_type VARCHAR(60) DEFAULT NULL;
ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id);
ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F911DD53E FOREIGN KEY (cv_files_manager_id) REFERENCES files_manager (id);
ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F30FCDC3A FOREIGN KEY (user_company_id) REFERENCES user_company (id);
CREATE UNIQUE INDEX UNIQ_FBF66A9F851953B4 ON announcement_tutor (announcement_group_id);
CREATE INDEX IDX_FBF66A9F911DD53E ON announcement_tutor (cv_files_manager_id);
CREATE INDEX IDX_FBF66A9F30FCDC3A ON announcement_tutor (user_company_id);
ALTER TABLE user ADD remote_roles JSON NOT NULL COMMENT '(DC2Type:json)', ADD custom_filters JSON DEFAULT NULL COMMENT '(DC2Type:json)', ADD timezone VARCHAR(150) DEFAULT NULL, ADD locale_campus VARCHAR(255) DEFAULT NULL, CHANGE roles roles JSON NOT NULL COMMENT '(DC2Type:json)', CHANGE meta meta JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE filter_category ADD sort INT NOT NULL, ADD is_ranking TINYINT(1) NOT NULL;
ALTER TABLE filter ADD source VARCHAR(10) DEFAULT NULL, ADD sort INT NOT NULL;
ALTER TABLE task ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD type VARCHAR(255) DEFAULT NULL, ADD updated_at DATETIME DEFAULT NULL, ADD deleted_at DATETIME DEFAULT NULL, CHANGE params params JSON DEFAULT NULL COMMENT '(DC2Type:json)', CHANGE created_at created_at DATETIME DEFAULT NULL;
ALTER TABLE task ADD CONSTRAINT FK_527EDB25B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE task ADD CONSTRAINT FK_527EDB25896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE task ADD CONSTRAINT FK_527EDB25C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
CREATE INDEX IDX_527EDB25B03A8386 ON task (created_by_id);
CREATE INDEX IDX_527EDB25896DBBDE ON task (updated_by_id);
CREATE INDEX IDX_527EDB25C76F1F52 ON task (deleted_by_id);
ALTER TABLE user_manage CHANGE centers centers JSON NOT NULL COMMENT '(DC2Type:json)', CHANGE countries countries JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE true_or_false CHANGE route image VARCHAR(255) DEFAULT NULL;
ALTER TABLE news CHANGE title title LONGTEXT NOT NULL, CHANGE text text LONGTEXT NOT NULL;
ALTER TABLE user_login ADD timezone VARCHAR(255) DEFAULT NULL, ADD country VARCHAR(255) DEFAULT NULL, ADD timezone_created_at DATETIME DEFAULT NULL, ADD timezone_updated_at DATETIME DEFAULT NULL;
ALTER TABLE help_category ADD sort INT NOT NULL;
ALTER TABLE parejas_imagen CHANGE imagen image VARCHAR(255) DEFAULT NULL;
ALTER TABLE nps ADD announcement_id INT DEFAULT NULL, ADD highlight TINYINT(1) DEFAULT NULL, CHANGE course_id course_id INT DEFAULT NULL;
ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
CREATE INDEX IDX_5B3B6648913AEA17 ON nps (announcement_id);
ALTER TABLE user_course_chapter ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD ip VARCHAR(255) DEFAULT NULL, ADD created_at DATETIME DEFAULT NULL, ADD deleted_at DATETIME DEFAULT NULL, ADD timezone VARCHAR(255) DEFAULT NULL, ADD country VARCHAR(255) DEFAULT NULL, ADD timezone_created_at DATETIME DEFAULT NULL, ADD timezone_updated_at DATETIME DEFAULT NULL, CHANGE data data JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9BB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9B896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9BC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
CREATE INDEX IDX_9AC76F9BB03A8386 ON user_course_chapter (created_by_id);
CREATE INDEX IDX_9AC76F9B896DBBDE ON user_course_chapter (updated_by_id);
CREATE INDEX IDX_9AC76F9BC76F1F52 ON user_course_chapter (deleted_by_id);
ALTER TABLE user_extra CHANGE category_id category_id INT DEFAULT NULL;
ALTER TABLE survey ADD meta LONGTEXT DEFAULT NULL COMMENT '(DC2Type:array)', ADD is_main TINYINT(1) DEFAULT NULL, CHANGE created_by_id created_by_id INT NOT NULL;
ALTER TABLE refresh_tokens ADD extra JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE scorm ADD raw_score INT DEFAULT 75 NOT NULL, ADD allow_reset TINYINT(1) DEFAULT 0 NOT NULL, CHANGE menu menu JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE itinerary ADD tags LONGTEXT DEFAULT NULL, ADD active TINYINT(1) NOT NULL, ADD sort INT DEFAULT NULL, CHANGE created_at created_at DATETIME NOT NULL;
ALTER TABLE challenge_duel_questions_adn CHANGE adn adn JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE nps_question ADD is_required TINYINT(1) DEFAULT NULL, ADD is_confidential TINYINT(1) DEFAULT NULL;
ALTER TABLE vcms_project CHANGE slides slides JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE documentation CHANGE roles roles JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE course ADD type_diploma_id INT DEFAULT NULL, ADD sort INT DEFAULT NULL, ADD is_content_diploma TINYINT(1) NOT NULL, ADD type_index_diploma VARCHAR(8) DEFAULT NULL, ADD description_content_diploma LONGTEXT DEFAULT NULL, ADD duration INT DEFAULT NULL, ADD diploma_config JSON NOT NULL COMMENT '(DC2Type:json)', CHANGE type_course type_course_id INT DEFAULT NULL, CHANGE new is_new TINYINT(1) NOT NULL;
ALTER TABLE course ADD CONSTRAINT FK_169E6FB9EDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id);
ALTER TABLE course ADD CONSTRAINT FK_169E6FB9B4991890 FOREIGN KEY (type_diploma_id) REFERENCES type_diploma (id);
CREATE INDEX IDX_169E6FB9EDDA8882 ON course (type_course_id);
CREATE INDEX IDX_169E6FB9B4991890 ON course (type_diploma_id);
ALTER TABLE course_category ADD order_type VARCHAR(20) DEFAULT NULL, ADD order_properties JSON DEFAULT NULL COMMENT '(DC2Type:json)', ADD description LONGTEXT DEFAULT NULL, ADD active TINYINT(1) NOT NULL;
ALTER TABLE course_section ADD type VARCHAR(100) DEFAULT NULL, ADD is_main TINYINT(1) DEFAULT NULL, ADD is_manual_selection TINYINT(1) DEFAULT NULL, ADD meta LONGTEXT DEFAULT NULL COMMENT '(DC2Type:array)';
ALTER TABLE course_category_translation ADD description LONGTEXT DEFAULT NULL;
ALTER TABLE user_roleplay_project CHANGE answers answers JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE chapter ADD is_active TINYINT(1) DEFAULT NULL;
ALTER TABLE user_vcms_project CHANGE actions_data actions_data JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE roleplay_scene CHANGE answers answers JSON NOT NULL COMMENT '(DC2Type:json)';
ALTER TABLE material_course ADD is_active TINYINT(1) DEFAULT 1 NOT NULL, ADD is_visible TINYINT(1) DEFAULT NULL, ADD type VARCHAR(255) DEFAULT NULL, ADD parent_id INT DEFAULT NULL;
ALTER TABLE task_user ADD timezone VARCHAR(255) DEFAULT NULL, ADD country VARCHAR(255) DEFAULT NULL, ADD timezone_created_at DATETIME DEFAULT NULL, ADD timezone_updated_at DATETIME DEFAULT NULL;
ALTER TABLE roleplay_ending ADD suspended_description LONGTEXT DEFAULT NULL;
ALTER TABLE notification CHANGE data data JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE user_course ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME DEFAULT NULL, ADD updated_at DATETIME DEFAULT NULL, ADD deleted_at DATETIME DEFAULT NULL, ADD timezone VARCHAR(255) DEFAULT NULL, ADD country VARCHAR(255) DEFAULT NULL, ADD timezone_created_at DATETIME DEFAULT NULL, ADD timezone_updated_at DATETIME DEFAULT NULL;
ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
CREATE INDEX IDX_73CC7484B03A8386 ON user_course (created_by_id);
CREATE INDEX IDX_73CC7484896DBBDE ON user_course (updated_by_id);
CREATE INDEX IDX_73CC7484C76F1F52 ON user_course (deleted_by_id);
ALTER TABLE user_token CHANGE extra extra JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE chapter_type ADD video VARCHAR(255) DEFAULT NULL, ADD video_en VARCHAR(255) DEFAULT NULL, ADD normalized VARCHAR(255) DEFAULT NULL, ADD percentage_completed NUMERIC(10, 2) DEFAULT NULL, ADD description LONGTEXT DEFAULT NULL, ADD code VARCHAR(50) DEFAULT NULL, ADD icon VARCHAR(100) DEFAULT NULL, ADD playerurl VARCHAR(255) DEFAULT NULL;
ALTER TABLE roleplay_beginning ADD attached_tag LONGTEXT DEFAULT NULL, ADD attached LONGTEXT DEFAULT NULL;
ALTER TABLE export ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, DROP created_by, DROP updated_by, CHANGE created_at created_at DATETIME DEFAULT NULL, CHANGE updated_at updated_at DATETIME DEFAULT NULL, CHANGE meta meta JSON DEFAULT NULL COMMENT '(DC2Type:json)', CHANGE deleted_by created_by_id INT DEFAULT NULL;
ALTER TABLE export ADD CONSTRAINT FK_428C1694B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id);
ALTER TABLE export ADD CONSTRAINT FK_428C1694896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id);
ALTER TABLE export ADD CONSTRAINT FK_428C1694C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id);
CREATE INDEX IDX_428C1694B03A8386 ON export (created_by_id);
CREATE INDEX IDX_428C1694896DBBDE ON export (updated_by_id);
CREATE INDEX IDX_428C1694C76F1F52 ON export (deleted_by_id);
ALTER TABLE user_time ADD announcement_id INT DEFAULT NULL, ADD ip VARCHAR(255) DEFAULT NULL, ADD updated_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', ADD timezone VARCHAR(255) DEFAULT NULL, ADD country VARCHAR(255) DEFAULT NULL, ADD timezone_created_at DATETIME DEFAULT NULL, ADD timezone_updated_at DATETIME DEFAULT NULL, CHANGE created_at created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', CHANGE extra extra JSON DEFAULT NULL COMMENT '(DC2Type:json)';
ALTER TABLE user_time ADD CONSTRAINT FK_1515D48C913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id);
CREATE INDEX IDX_1515D48C913AEA17 ON user_time (announcement_id);
