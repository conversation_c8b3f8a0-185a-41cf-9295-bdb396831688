-- MySQL dump 10.13  Distrib 8.0.19, for Win64 (x86_64)
--
-- Host: localhost    Database: ecovalores_pro
-- ------------------------------------------------------
-- Server version	5.5.5-10.5.24-MariaDB-1:10.5.24+maria~ubu2004

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `adivina_imagen`
--

DROP TABLE IF EXISTS `adivina_imagen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `adivina_imagen` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `image` varchar(255) NOT NULL,
  `words` varchar(255) NOT NULL,
  `clue` varchar(255) DEFAULT NULL,
  `time` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_EF1B569E579F4768` (`chapter_id`),
  CONSTRAINT `FK_EF1B569EFF0D08E8` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcement`
--

DROP TABLE IF EXISTS `announcement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `start_at` datetime NOT NULL,
  `finish_at` datetime NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `subsidized` tinyint(1) NOT NULL,
  `max_users` int(11) DEFAULT NULL,
  `subsidizer_id` int(11) DEFAULT NULL,
  `formative_action_type` varchar(255) DEFAULT NULL,
  `format` varchar(255) DEFAULT NULL,
  `total_hours` int(11) DEFAULT NULL,
  `place` varchar(255) DEFAULT NULL,
  `training_center` varchar(255) DEFAULT NULL,
  `training_center_address` varchar(255) DEFAULT NULL,
  `training_center_nif` varchar(255) DEFAULT NULL,
  `training_center_teacher` varchar(255) DEFAULT NULL,
  `training_center_teacher_dni` varchar(255) DEFAULT NULL,
  `training_center_phone` varchar(255) DEFAULT NULL,
  `training_center_email` varchar(255) DEFAULT NULL,
  `subsidizer_entity` varchar(255) DEFAULT NULL,
  `general_information` text DEFAULT NULL,
  `s_group` varchar(20) DEFAULT NULL,
  `action_denomination` varchar(20) DEFAULT NULL,
  `modality` varchar(20) DEFAULT NULL,
  `place_of_instruction` varchar(20) DEFAULT NULL,
  `collaboration_type` varchar(20) DEFAULT NULL,
  `provider` varchar(20) DEFAULT NULL,
  `provider_cif` varchar(20) DEFAULT NULL,
  `hotel` varchar(255) DEFAULT NULL,
  `society` varchar(255) DEFAULT NULL,
  `subsidized_group` varchar(255) DEFAULT NULL,
  `department` varchar(255) DEFAULT NULL,
  `client_contact_person` varchar(255) DEFAULT NULL,
  `af_type` varchar(20) DEFAULT NULL,
  `formation_level` varchar(20) DEFAULT NULL,
  `number_of_participants_af` int(11) DEFAULT NULL,
  `number_of_groups` int(11) DEFAULT NULL,
  `directed_to` varchar(255) DEFAULT NULL,
  `selection_criteria` varchar(255) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `trainer` varchar(255) DEFAULT NULL,
  `trainer_center` varchar(255) DEFAULT NULL,
  `trainer_center_cif` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_4DB9D91C591CC992` (`course_id`),
  KEY `IDX_4DB9D91CB03A8386` (`created_by_id`),
  KEY `IDX_4DB9D91C896DBBDE` (`updated_by_id`),
  KEY `IDX_4DB9D91CC76F1F52` (`deleted_by_id`),
  KEY `IDX_4DB9D91CB8778369` (`subsidizer_id`),
  CONSTRAINT `FK_4DB9D91C591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_4DB9D91C896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_4DB9D91CB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_4DB9D91CB8778369` FOREIGN KEY (`subsidizer_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_4DB9D91CC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcement_observation`
--

DROP TABLE IF EXISTS `announcement_observation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcement_observation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `course_status` varchar(255) DEFAULT NULL,
  `comunicado_fundae` tinyint(1) NOT NULL,
  `comunicado_abilitia` tinyint(1) NOT NULL,
  `provider_cost` double NOT NULL,
  `hedima_management_cost` double NOT NULL,
  `travel_and_maintenance_cost` double NOT NULL,
  `total_cost` double NOT NULL,
  `economic_module` varchar(255) DEFAULT NULL,
  `final_pax` double NOT NULL,
  `maximum_bonus` double NOT NULL,
  `subsidized_amount` double NOT NULL,
  `private_amount` double NOT NULL,
  `provider_invoice_number` varchar(100) DEFAULT NULL,
  `hedima_management_invoice_number` varchar(100) DEFAULT NULL,
  `travel_and_maintenance` varchar(255) DEFAULT NULL,
  `invoice_status` varchar(255) DEFAULT NULL,
  `observations` longtext DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BBF55BFE913AEA17` (`announcement_id`),
  KEY `IDX_BBF55BFEB03A8386` (`created_by_id`),
  KEY `IDX_BBF55BFE896DBBDE` (`updated_by_id`),
  KEY `IDX_BBF55BFEC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_BBF55BFE896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_BBF55BFE913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_BBF55BFEB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_BBF55BFEC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcement_observation_document`
--

DROP TABLE IF EXISTS `announcement_observation_document`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcement_observation_document` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_observation_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `type` varchar(20) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(60) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_E69ACE464D83195D` (`announcement_observation_id`),
  KEY `IDX_E69ACE46B03A8386` (`created_by_id`),
  KEY `IDX_E69ACE46896DBBDE` (`updated_by_id`),
  KEY `IDX_E69ACE46C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_E69ACE464D83195D` FOREIGN KEY (`announcement_observation_id`) REFERENCES `announcement_observation` (`id`),
  CONSTRAINT `FK_E69ACE46896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_E69ACE46B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_E69ACE46C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcement_tutor`
--

DROP TABLE IF EXISTS `announcement_tutor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcement_tutor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_id` int(11) NOT NULL,
  `tutor_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_FBF66A9F913AEA17` (`announcement_id`),
  KEY `IDX_FBF66A9F208F64F1` (`tutor_id`),
  CONSTRAINT `FK_FBF66A9F208F64F1` FOREIGN KEY (`tutor_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FBF66A9F913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcement_user`
--

DROP TABLE IF EXISTS `announcement_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcement_user` (
  `announcement_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `notified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A1A2DE15913AEA17` (`announcement_id`),
  KEY `IDX_A1A2DE15A76ED395` (`user_id`),
  CONSTRAINT `FK_A1A2DE15913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_A1A2DE15A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `answer`
--

DROP TABLE IF EXISTS `answer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `answer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL,
  `answer` longtext NOT NULL,
  `correct` tinyint(1) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_DADD4A251E27F6BF` (`question_id`),
  KEY `IDX_DADD4A25B03A8386` (`created_by_id`),
  KEY `IDX_DADD4A25896DBBDE` (`updated_by_id`),
  KEY `IDX_DADD4A25C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_DADD4A251E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `question` (`id`),
  CONSTRAINT `FK_DADD4A25896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_DADD4A25B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_DADD4A25C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `answers_video_quiz`
--

DROP TABLE IF EXISTS `answers_video_quiz`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `answers_video_quiz` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) DEFAULT NULL,
  `answer` varchar(255) NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_1CFB516C1E27F6BF` (`question_id`),
  CONSTRAINT `FK_1CFB516C1E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `videopreguntas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bots`
--

DROP TABLE IF EXISTS `bots`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `route` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categorize`
--

DROP TABLE IF EXISTS `categorize`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categorize` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question` varchar(255) DEFAULT NULL,
  `chapter_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_37DFDA7579F4768` (`chapter_id`),
  KEY `IDX_37DFDA7B03A8386` (`created_by_id`),
  KEY `IDX_37DFDA7896DBBDE` (`updated_by_id`),
  KEY `IDX_37DFDA7C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_37DFDA7579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_37DFDA7896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_37DFDA7B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_37DFDA7C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categorize_answers`
--

DROP TABLE IF EXISTS `categorize_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categorize_answers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `categorize_id` int(11) DEFAULT NULL,
  `options_id` int(11) DEFAULT NULL,
  `correct` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_71A5F63835D01CA7` (`categorize_id`),
  KEY `IDX_71A5F6383ADB05F1` (`options_id`),
  CONSTRAINT `FK_71A5F63835D01CA7` FOREIGN KEY (`categorize_id`) REFERENCES `categorize` (`id`),
  CONSTRAINT `FK_71A5F6383ADB05F1` FOREIGN KEY (`options_id`) REFERENCES `categorize_options` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categorize_options`
--

DROP TABLE IF EXISTS `categorize_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categorize_options` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_F140CAB9579F4768` (`chapter_id`),
  KEY `IDX_F140CAB9B03A8386` (`created_by_id`),
  KEY `IDX_F140CAB9896DBBDE` (`updated_by_id`),
  KEY `IDX_F140CAB9C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_F140CAB9579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_F140CAB9896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_F140CAB9B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_F140CAB9C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `center`
--

DROP TABLE IF EXISTS `center`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `center` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge`
--

DROP TABLE IF EXISTS `challenge`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `announcement_id` int(11) DEFAULT NULL,
  `locale` varchar(10) NOT NULL,
  `translation_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_D7098951B03A8386` (`created_by_id`),
  KEY `IDX_D7098951896DBBDE` (`updated_by_id`),
  KEY `IDX_D7098951C76F1F52` (`deleted_by_id`),
  KEY `IDX_D7098951913AEA17` (`announcement_id`),
  KEY `IDX_D70989519CAA2B25` (`translation_id`),
  CONSTRAINT `FK_D7098951896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_D7098951913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_D70989519CAA2B25` FOREIGN KEY (`translation_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_D7098951B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_D7098951C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_answers`
--

DROP TABLE IF EXISTS `challenge_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_answers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pregunta_id` int(11) DEFAULT NULL,
  `text` varchar(255) NOT NULL,
  `correct` tinyint(1) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BC40898331A5801E` (`pregunta_id`),
  KEY `IDX_BC408983B03A8386` (`created_by_id`),
  KEY `IDX_BC408983896DBBDE` (`updated_by_id`),
  KEY `IDX_BC408983C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_BC40898331A5801E` FOREIGN KEY (`pregunta_id`) REFERENCES `challenge_questions` (`id`),
  CONSTRAINT `FK_BC408983896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_BC408983B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_BC408983C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_answers_translation`
--

DROP TABLE IF EXISTS `challenge_answers_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_answers_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `text` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `challenge_answers_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_C1CB75B22C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_C1CB75B22C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `challenge_answers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_duel`
--

DROP TABLE IF EXISTS `challenge_duel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_duel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `challenge_id` int(11) DEFAULT NULL,
  `user1_id` int(11) DEFAULT NULL,
  `user2_id` int(11) DEFAULT NULL,
  `winner` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `assigned_bot_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_921BA0E498A21AC6` (`challenge_id`),
  KEY `IDX_921BA0E456AE248B` (`user1_id`),
  KEY `IDX_921BA0E4441B8B65` (`user2_id`),
  KEY `IDX_921BA0E4B03A8386` (`created_by_id`),
  KEY `IDX_921BA0E4896DBBDE` (`updated_by_id`),
  KEY `IDX_921BA0E4C76F1F52` (`deleted_by_id`),
  KEY `IDX_921BA0E4AD76C09` (`assigned_bot_id`),
  CONSTRAINT `FK_921BA0E4441B8B65` FOREIGN KEY (`user2_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_921BA0E456AE248B` FOREIGN KEY (`user1_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_921BA0E4896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_921BA0E498A21AC6` FOREIGN KEY (`challenge_id`) REFERENCES `challenge` (`id`),
  CONSTRAINT `FK_921BA0E4AD76C09` FOREIGN KEY (`assigned_bot_id`) REFERENCES `bots` (`id`),
  CONSTRAINT `FK_921BA0E4B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_921BA0E4C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_duel_questions`
--

DROP TABLE IF EXISTS `challenge_duel_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_duel_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `duel_id` int(11) NOT NULL,
  `answer_user1_id` int(11) DEFAULT NULL,
  `answer_user2_id` int(11) DEFAULT NULL,
  `time_user1` int(11) DEFAULT NULL,
  `time_user2` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_446B904758875E` (`duel_id`),
  KEY `IDX_446B90471E27F6BF` (`question_id`),
  KEY `IDX_446B904771E425EE` (`answer_user1_id`),
  KEY `IDX_446B904763518A00` (`answer_user2_id`),
  CONSTRAINT `FK_446B90471E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `challenge_questions` (`id`),
  CONSTRAINT `FK_446B904758875E` FOREIGN KEY (`duel_id`) REFERENCES `challenge_duel` (`id`),
  CONSTRAINT `FK_446B904763518A00` FOREIGN KEY (`answer_user2_id`) REFERENCES `challenge_answers` (`id`),
  CONSTRAINT `FK_446B904771E425EE` FOREIGN KEY (`answer_user1_id`) REFERENCES `challenge_answers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_duel_questions_adn`
--

DROP TABLE IF EXISTS `challenge_duel_questions_adn`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_duel_questions_adn` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `challenge_id` int(11) NOT NULL,
  `adn` longtext NOT NULL COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  KEY `IDX_5CCC6C92A76ED395` (`user_id`),
  KEY `IDX_5CCC6C9298A21AC6` (`challenge_id`),
  CONSTRAINT `FK_5CCC6C9298A21AC6` FOREIGN KEY (`challenge_id`) REFERENCES `challenge` (`id`),
  CONSTRAINT `FK_5CCC6C92A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_questions`
--

DROP TABLE IF EXISTS `challenge_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `desafio_id` int(11) NOT NULL,
  `random` tinyint(1) NOT NULL,
  `text` varchar(500) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_2A4B884A92A6DBB9` (`desafio_id`),
  KEY `IDX_2A4B884AB03A8386` (`created_by_id`),
  KEY `IDX_2A4B884A896DBBDE` (`updated_by_id`),
  KEY `IDX_2A4B884AC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_2A4B884A896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2A4B884A92A6DBB9` FOREIGN KEY (`desafio_id`) REFERENCES `challenge` (`id`),
  CONSTRAINT `FK_2A4B884AB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2A4B884AC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_questions_translation`
--

DROP TABLE IF EXISTS `challenge_questions_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_questions_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `text` varchar(500) NOT NULL,
  `translatable_id` int(11) DEFAULT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `challenge_questions_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_8C2788B02C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_8C2788B02C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `challenge_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_user`
--

DROP TABLE IF EXISTS `challenge_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `challenge_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `notified` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_843CD1CF98A21AC6` (`challenge_id`),
  KEY `IDX_843CD1CFA76ED395` (`user_id`),
  KEY `IDX_843CD1CFB03A8386` (`created_by_id`),
  KEY `IDX_843CD1CF896DBBDE` (`updated_by_id`),
  KEY `IDX_843CD1CFC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_843CD1CF896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_843CD1CF98A21AC6` FOREIGN KEY (`challenge_id`) REFERENCES `challenge` (`id`),
  CONSTRAINT `FK_843CD1CFA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_843CD1CFB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_843CD1CFC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `challenge_user_points`
--

DROP TABLE IF EXISTS `challenge_user_points`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `challenge_user_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `challenge_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `points` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3B543DA998A21AC6` (`challenge_id`),
  KEY `IDX_3B543DA9A76ED395` (`user_id`),
  CONSTRAINT `FK_3B543DA998A21AC6` FOREIGN KEY (`challenge_id`) REFERENCES `challenge` (`id`),
  CONSTRAINT `FK_3B543DA9A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chapter`
--

DROP TABLE IF EXISTS `chapter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chapter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `type_id` int(11) NOT NULL,
  `position` int(11) NOT NULL,
  `season_id` int(11) DEFAULT NULL,
  `vcms_project_id` int(11) DEFAULT NULL,
  `roleplay_project_id` int(11) DEFAULT NULL,
  `max_question` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_F981B52E7631067` (`vcms_project_id`),
  UNIQUE KEY `UNIQ_F981B52EB6704F40` (`roleplay_project_id`),
  KEY `IDX_F981B52E591CC992` (`course_id`),
  KEY `IDX_F981B52EB03A8386` (`created_by_id`),
  KEY `IDX_F981B52E896DBBDE` (`updated_by_id`),
  KEY `IDX_F981B52EC76F1F52` (`deleted_by_id`),
  KEY `IDX_F981B52EC54C8C93` (`type_id`),
  KEY `IDX_F981B52E4EC001D1` (`season_id`),
  CONSTRAINT `FK_F981B52E4EC001D1` FOREIGN KEY (`season_id`) REFERENCES `season` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_F981B52E591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_F981B52E7631067` FOREIGN KEY (`vcms_project_id`) REFERENCES `vcms_project` (`id`),
  CONSTRAINT `FK_F981B52E896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_F981B52EB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_F981B52EB6704F40` FOREIGN KEY (`roleplay_project_id`) REFERENCES `roleplay_project` (`id`),
  CONSTRAINT `FK_F981B52EC54C8C93` FOREIGN KEY (`type_id`) REFERENCES `chapter_type` (`id`),
  CONSTRAINT `FK_F981B52EC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `chapter_type`
--

DROP TABLE IF EXISTS `chapter_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chapter_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(60) NOT NULL,
  `active` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `comment_task`
--

DROP TABLE IF EXISTS `comment_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comment_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `history_delivery_task_id` int(11) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `comment` longtext DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_744879C95445F77F` (`history_delivery_task_id`),
  KEY `IDX_744879C9727ACA70` (`parent_id`),
  KEY `IDX_744879C9B03A8386` (`created_by_id`),
  KEY `IDX_744879C9896DBBDE` (`updated_by_id`),
  KEY `IDX_744879C9C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_744879C95445F77F` FOREIGN KEY (`history_delivery_task_id`) REFERENCES `history_delivery_task` (`id`),
  CONSTRAINT `FK_744879C9727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `comment_task` (`id`),
  CONSTRAINT `FK_744879C9896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_744879C9B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_744879C9C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `content`
--

DROP TABLE IF EXISTS `content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `position` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_FEC530A9579F4768` (`chapter_id`),
  KEY `IDX_FEC530A9B03A8386` (`created_by_id`),
  KEY `IDX_FEC530A9896DBBDE` (`updated_by_id`),
  KEY `IDX_FEC530A9C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_FEC530A9579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_FEC530A9896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FEC530A9B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FEC530A9C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course`
--

DROP TABLE IF EXISTS `course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `open` tinyint(1) NOT NULL,
  `locale` varchar(10) NOT NULL,
  `translation_id` int(11) DEFAULT NULL,
  `points` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `general_information` longtext DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `level_id` int(11) DEFAULT NULL,
  `documentation` longtext DEFAULT NULL,
  `open_visible` tinyint(1) DEFAULT NULL,
  `new` tinyint(1) NOT NULL,
  `new_at` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  `type_course` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_169E6FB9B03A8386` (`created_by_id`),
  KEY `IDX_169E6FB9896DBBDE` (`updated_by_id`),
  KEY `IDX_169E6FB9C76F1F52` (`deleted_by_id`),
  KEY `IDX_169E6FB99CAA2B25` (`translation_id`),
  KEY `IDX_169E6FB912469DE2` (`category_id`),
  KEY `IDX_169E6FB95FB14BA7` (`level_id`),
  CONSTRAINT `FK_169E6FB912469DE2` FOREIGN KEY (`category_id`) REFERENCES `course_category` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_169E6FB95FB14BA7` FOREIGN KEY (`level_id`) REFERENCES `course_level` (`id`),
  CONSTRAINT `FK_169E6FB9896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_169E6FB99CAA2B25` FOREIGN KEY (`translation_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_169E6FB9B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_169E6FB9C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_category`
--

DROP TABLE IF EXISTS `course_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_AFF87497727ACA70` (`parent_id`),
  CONSTRAINT `FK_AFF87497727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `course_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_category_translation`
--

DROP TABLE IF EXISTS `course_category_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_category_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `course_category_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_84EE3C232C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_84EE3C232C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `course_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_filter`
--

DROP TABLE IF EXISTS `course_filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_filter` (
  `course_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL,
  PRIMARY KEY (`course_id`,`filter_id`),
  KEY `IDX_D1FC9E50591CC992` (`course_id`),
  KEY `IDX_D1FC9E50D395B25E` (`filter_id`),
  CONSTRAINT `FK_D1FC9E50591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_D1FC9E50D395B25E` FOREIGN KEY (`filter_id`) REFERENCES `filter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_level`
--

DROP TABLE IF EXISTS `course_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_level` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_7341CBFBB03A8386` (`created_by_id`),
  KEY `IDX_7341CBFB896DBBDE` (`updated_by_id`),
  KEY `IDX_7341CBFBC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_7341CBFB896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7341CBFBB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7341CBFBC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_manager`
--

DROP TABLE IF EXISTS `course_manager`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_manager` (
  `course_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`course_id`,`user_id`),
  KEY `IDX_F2E72055591CC992` (`course_id`),
  KEY `IDX_F2E72055A76ED395` (`user_id`),
  CONSTRAINT `FK_F2E72055591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_F2E72055A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_professional_category`
--

DROP TABLE IF EXISTS `course_professional_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_professional_category` (
  `course_id` int(11) NOT NULL,
  `professional_category_id` int(11) NOT NULL,
  PRIMARY KEY (`course_id`,`professional_category_id`),
  KEY `IDX_30476A2C591CC992` (`course_id`),
  KEY `IDX_30476A2CA0CCFBB2` (`professional_category_id`),
  CONSTRAINT `FK_30476A2C591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_30476A2CA0CCFBB2` FOREIGN KEY (`professional_category_id`) REFERENCES `professional_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_section`
--

DROP TABLE IF EXISTS `course_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `active` tinyint(1) NOT NULL,
  `sort` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `hide_category_name` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_25B07F03989D9B62` (`slug`),
  KEY `IDX_25B07F03B03A8386` (`created_by_id`),
  KEY `IDX_25B07F03896DBBDE` (`updated_by_id`),
  KEY `IDX_25B07F03C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_25B07F03896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_25B07F03B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_25B07F03C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_section_course_category`
--

DROP TABLE IF EXISTS `course_section_course_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_section_course_category` (
  `course_section_id` int(11) NOT NULL,
  `course_category_id` int(11) NOT NULL,
  PRIMARY KEY (`course_section_id`,`course_category_id`),
  KEY `IDX_2FCB6D087C1ADF9` (`course_section_id`),
  KEY `IDX_2FCB6D086628AD36` (`course_category_id`),
  CONSTRAINT `FK_2FCB6D086628AD36` FOREIGN KEY (`course_category_id`) REFERENCES `course_category` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_2FCB6D087C1ADF9` FOREIGN KEY (`course_section_id`) REFERENCES `course_section` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_section_translation`
--

DROP TABLE IF EXISTS `course_section_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_section_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `course_section_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_B600CF812C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_B600CF812C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `course_section` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_segment`
--

DROP TABLE IF EXISTS `course_segment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_segment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `course_segment_category_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_1042F089B03A8386` (`created_by_id`),
  KEY `IDX_1042F089896DBBDE` (`updated_by_id`),
  KEY `IDX_1042F089C76F1F52` (`deleted_by_id`),
  KEY `IDX_1042F089ED988ED` (`course_segment_category_id`),
  CONSTRAINT `FK_1042F089896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_1042F089B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_1042F089C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_1042F089ED988ED` FOREIGN KEY (`course_segment_category_id`) REFERENCES `course_segment_category` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_segment_category`
--

DROP TABLE IF EXISTS `course_segment_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_segment_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_segment_category_translation`
--

DROP TABLE IF EXISTS `course_segment_category_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_segment_category_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `course_segment_category_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_858433AF2C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_858433AF2C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `course_segment_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_segment_course`
--

DROP TABLE IF EXISTS `course_segment_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_segment_course` (
  `course_segment_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  PRIMARY KEY (`course_segment_id`,`course_id`),
  KEY `IDX_959B62B44CB242E` (`course_segment_id`),
  KEY `IDX_959B62B4591CC992` (`course_id`),
  CONSTRAINT `FK_959B62B44CB242E` FOREIGN KEY (`course_segment_id`) REFERENCES `course_segment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_959B62B4591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_segment_translation`
--

DROP TABLE IF EXISTS `course_segment_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_segment_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `course_segment_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_A8F650602C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_A8F650602C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `course_segment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `course_tag`
--

DROP TABLE IF EXISTS `course_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_tag` (
  `course_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  PRIMARY KEY (`course_id`,`tag_id`),
  KEY `IDX_760531B1591CC992` (`course_id`),
  KEY `IDX_760531B1BAD26311` (`tag_id`),
  CONSTRAINT `FK_760531B1591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_760531B1BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cron_job`
--

DROP TABLE IF EXISTS `cron_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cron_job` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `command` varchar(1024) NOT NULL,
  `schedule` varchar(191) NOT NULL,
  `description` varchar(191) NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `un_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cron_report`
--

DROP TABLE IF EXISTS `cron_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cron_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_id` int(11) DEFAULT NULL,
  `run_at` datetime NOT NULL,
  `run_time` double NOT NULL,
  `exit_code` int(11) NOT NULL,
  `output` longtext NOT NULL,
  `error` longtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B6C6A7F5BE04EA9` (`job_id`),
  CONSTRAINT `FK_B6C6A7F5BE04EA9` FOREIGN KEY (`job_id`) REFERENCES `cron_job` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `department`
--

DROP TABLE IF EXISTS `department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `documentation`
--

DROP TABLE IF EXISTS `documentation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `documentation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` longtext NOT NULL,
  `roles` longtext NOT NULL COMMENT '(DC2Type:json)',
  `type` varchar(10) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `file` varchar(255) DEFAULT NULL,
  `locale` varchar(20) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `position` int(11) NOT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `video_identifier` varchar(100) DEFAULT NULL,
  `filename` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_73D5A93BB03A8386` (`created_by_id`),
  KEY `IDX_73D5A93B896DBBDE` (`updated_by_id`),
  KEY `IDX_73D5A93BC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_73D5A93B896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_73D5A93BB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_73D5A93BC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_recipient`
--

DROP TABLE IF EXISTS `email_recipient`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_recipient` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_670F6462A76ED395` (`user_id`),
  KEY `IDX_670F64625DA0FB8` (`template_id`),
  CONSTRAINT `FK_670F64625DA0FB8` FOREIGN KEY (`template_id`) REFERENCES `email_template` (`id`),
  CONSTRAINT `FK_670F6462A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_template`
--

DROP TABLE IF EXISTS `email_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `subject` varchar(255) NOT NULL,
  `body` longtext NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_9C0600CAB03A8386` (`created_by_id`),
  KEY `IDX_9C0600CA896DBBDE` (`updated_by_id`),
  KEY `IDX_9C0600CAC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_9C0600CA896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_9C0600CAB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_9C0600CAC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `export`
--

DROP TABLE IF EXISTS `export`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `export` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` int(11) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  `available_until` datetime DEFAULT NULL,
  `filename` varchar(255) DEFAULT NULL,
  `meta` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  `task_id` int(11) DEFAULT NULL,
  `type` varchar(50) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UNIQ_428C16948DB60186` (`task_id`),
  CONSTRAINT `fk_export_task_id` FOREIGN KEY (`task_id`) REFERENCES `task` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `files_history_task`
--

DROP TABLE IF EXISTS `files_history_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `files_history_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `history_delivery_task_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(60) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_E94FD4805445F77F` (`history_delivery_task_id`),
  KEY `IDX_E94FD480B03A8386` (`created_by_id`),
  KEY `IDX_E94FD480896DBBDE` (`updated_by_id`),
  KEY `IDX_E94FD480C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_E94FD4805445F77F` FOREIGN KEY (`history_delivery_task_id`) REFERENCES `history_delivery_task` (`id`),
  CONSTRAINT `FK_E94FD480896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_E94FD480B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_E94FD480C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `files_task`
--

DROP TABLE IF EXISTS `files_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `files_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_course_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `type_material` varchar(255) DEFAULT NULL,
  `url_material` longtext DEFAULT NULL,
  `is_download` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(60) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_D5B6DE44263604C3` (`task_course_id`),
  KEY `IDX_D5B6DE44B03A8386` (`created_by_id`),
  KEY `IDX_D5B6DE44896DBBDE` (`updated_by_id`),
  KEY `IDX_D5B6DE44C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_D5B6DE44263604C3` FOREIGN KEY (`task_course_id`) REFERENCES `task_course` (`id`),
  CONSTRAINT `FK_D5B6DE44896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_D5B6DE44B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_D5B6DE44C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `fillgaps`
--

DROP TABLE IF EXISTS `fillgaps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fillgaps` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `text` longtext NOT NULL,
  `answers` longtext NOT NULL,
  `time` int(11) NOT NULL,
  `label` varchar(255) NOT NULL,
  `extra` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_47AB1DD3579F4768` (`chapter_id`),
  CONSTRAINT `FK_47AB1DD3579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `filter`
--

DROP TABLE IF EXISTS `filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `filter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter_category_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_7FC45F1D6B444833` (`filter_category_id`),
  KEY `IDX_7FC45F1D727ACA70` (`parent_id`),
  CONSTRAINT `FK_7FC45F1D6B444833` FOREIGN KEY (`filter_category_id`) REFERENCES `filter_category` (`id`),
  CONSTRAINT `FK_7FC45F1D727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `filter` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `filter_category`
--

DROP TABLE IF EXISTS `filter_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `filter_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3B231C61727ACA70` (`parent_id`),
  CONSTRAINT `FK_3B231C61727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `filter_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `filter_category_translation`
--

DROP TABLE IF EXISTS `filter_category_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `filter_category_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `filter_category_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_F5C167262C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_F5C167262C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `filter_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `filter_translation`
--

DROP TABLE IF EXISTS `filter_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `filter_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `filter_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_CD6B90BE2C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_CD6B90BE2C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `filter` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forum_likes`
--

DROP TABLE IF EXISTS `forum_likes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forum_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `forum_post_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  PRIMARY KEY (`id`),
  KEY `IDX_51BEE8AABA454E5D` (`forum_post_id`),
  KEY `IDX_51BEE8AAA76ED395` (`user_id`),
  CONSTRAINT `FK_51BEE8AAA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_51BEE8AABA454E5D` FOREIGN KEY (`forum_post_id`) REFERENCES `forum_post` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forum_post`
--

DROP TABLE IF EXISTS `forum_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forum_post` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `announcement_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `message` longtext NOT NULL,
  `last_response_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `response_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_996BCC5AA76ED395` (`user_id`),
  KEY `IDX_996BCC5A727ACA70` (`parent_id`),
  KEY `IDX_996BCC5A591CC992` (`course_id`),
  KEY `IDX_996BCC5A913AEA17` (`announcement_id`),
  KEY `IDX_996BCC5AB03A8386` (`created_by_id`),
  KEY `IDX_996BCC5A896DBBDE` (`updated_by_id`),
  KEY `IDX_996BCC5AC76F1F52` (`deleted_by_id`),
  KEY `IDX_996BCC5AFBF32840` (`response_id`),
  CONSTRAINT `FK_996BCC5A591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_996BCC5A727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `forum_post` (`id`),
  CONSTRAINT `FK_996BCC5A896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_996BCC5A913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_996BCC5AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_996BCC5AB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_996BCC5AC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_996BCC5AFBF32840` FOREIGN KEY (`response_id`) REFERENCES `forum_post` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forum_report`
--

DROP TABLE IF EXISTS `forum_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `forum_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `forum_post_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  PRIMARY KEY (`id`),
  KEY `IDX_DC804455BA454E5D` (`forum_post_id`),
  KEY `IDX_DC804455A76ED395` (`user_id`),
  CONSTRAINT `FK_DC804455A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_DC804455BA454E5D` FOREIGN KEY (`forum_post_id`) REFERENCES `forum_post` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gamesword`
--

DROP TABLE IF EXISTS `gamesword`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gamesword` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `question` varchar(255) NOT NULL,
  `word` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  `gametype` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_2CD5DB38579F4768` (`chapter_id`),
  CONSTRAINT `FK_2CD5DB38579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `guessword`
--

DROP TABLE IF EXISTS `guessword`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `guessword` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `question` varchar(255) NOT NULL,
  `word` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_32444B08579F4768` (`chapter_id`),
  CONSTRAINT `FK_32444B08579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `help_category`
--

DROP TABLE IF EXISTS `help_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `help_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `help_category_translation`
--

DROP TABLE IF EXISTS `help_category_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `help_category_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `help_category_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_ED35F7482C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_ED35F7482C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `help_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `help_text`
--

DROP TABLE IF EXISTS `help_text`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `help_text` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `text` longtext NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_7E01925C12469DE2` (`category_id`),
  CONSTRAINT `FK_7E01925C12469DE2` FOREIGN KEY (`category_id`) REFERENCES `help_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `help_text_translation`
--

DROP TABLE IF EXISTS `help_text_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `help_text_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `text` longtext NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `help_text_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_857DDBF02C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_857DDBF02C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `help_text` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `higher_lower`
--

DROP TABLE IF EXISTS `higher_lower`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `higher_lower` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_7C0E1150579F4768` (`chapter_id`),
  KEY `IDX_7C0E1150B03A8386` (`created_by_id`),
  KEY `IDX_7C0E1150896DBBDE` (`updated_by_id`),
  KEY `IDX_7C0E1150C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_7C0E1150579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_7C0E1150896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7C0E1150B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7C0E1150C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `higher_lower_words`
--

DROP TABLE IF EXISTS `higher_lower_words`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `higher_lower_words` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `higher_lower_id` int(11) DEFAULT NULL,
  `position` int(11) NOT NULL,
  `word` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_177FA2D2BE9CE5EB` (`higher_lower_id`),
  CONSTRAINT `FK_177FA2D2BE9CE5EB` FOREIGN KEY (`higher_lower_id`) REFERENCES `higher_lower` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `history_delivery_task`
--

DROP TABLE IF EXISTS `history_delivery_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `history_delivery_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_user_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `state` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A9B9C8D8B88FF97F` (`task_user_id`),
  KEY `IDX_A9B9C8D8B03A8386` (`created_by_id`),
  KEY `IDX_A9B9C8D8896DBBDE` (`updated_by_id`),
  KEY `IDX_A9B9C8D8C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_A9B9C8D8896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_A9B9C8D8B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_A9B9C8D8B88FF97F` FOREIGN KEY (`task_user_id`) REFERENCES `task_user` (`id`),
  CONSTRAINT `FK_A9B9C8D8C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `history_seen_material`
--

DROP TABLE IF EXISTS `history_seen_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `history_seen_material` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `material_course_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_C83ABC55591CC992` (`course_id`),
  KEY `IDX_C83ABC55A76ED395` (`user_id`),
  KEY `IDX_C83ABC556182D372` (`material_course_id`),
  CONSTRAINT `FK_C83ABC55591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_C83ABC556182D372` FOREIGN KEY (`material_course_id`) REFERENCES `material_course` (`id`),
  CONSTRAINT `FK_C83ABC55A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `holes`
--

DROP TABLE IF EXISTS `holes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `holes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fillgap_id` int(11) DEFAULT NULL,
  `hole` int(11) NOT NULL,
  `answer` varchar(255) NOT NULL,
  `correct` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_9C6EA1E4477D657A` (`fillgap_id`),
  CONSTRAINT `FK_9C6EA1E4477D657A` FOREIGN KEY (`fillgap_id`) REFERENCES `fillgaps` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `itinerary`
--

DROP TABLE IF EXISTS `itinerary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `itinerary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_FF2238F6B03A8386` (`created_by_id`),
  KEY `IDX_FF2238F6896DBBDE` (`updated_by_id`),
  KEY `IDX_FF2238F6C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_FF2238F6896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FF2238F6B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FF2238F6C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `itinerary_course`
--

DROP TABLE IF EXISTS `itinerary_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `itinerary_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itinerary_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `position` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_652E788C15F737B2` (`itinerary_id`),
  KEY `IDX_652E788C591CC992` (`course_id`),
  CONSTRAINT `FK_652E788C15F737B2` FOREIGN KEY (`itinerary_id`) REFERENCES `itinerary` (`id`),
  CONSTRAINT `FK_652E788C591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `itinerary_filter`
--

DROP TABLE IF EXISTS `itinerary_filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `itinerary_filter` (
  `itinerary_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL,
  PRIMARY KEY (`itinerary_id`,`filter_id`),
  KEY `IDX_C74482815F737B2` (`itinerary_id`),
  KEY `IDX_C744828D395B25E` (`filter_id`),
  CONSTRAINT `FK_C74482815F737B2` FOREIGN KEY (`itinerary_id`) REFERENCES `itinerary` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_C744828D395B25E` FOREIGN KEY (`filter_id`) REFERENCES `filter` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `itinerary_manager`
--

DROP TABLE IF EXISTS `itinerary_manager`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `itinerary_manager` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itinerary_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_ACE4518D15F737B2` (`itinerary_id`),
  KEY `IDX_ACE4518DA76ED395` (`user_id`),
  CONSTRAINT `FK_ACE4518D15F737B2` FOREIGN KEY (`itinerary_id`) REFERENCES `itinerary` (`id`),
  CONSTRAINT `FK_ACE4518DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `itinerary_user`
--

DROP TABLE IF EXISTS `itinerary_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `itinerary_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itinerary_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_8AB0BC1D15F737B2` (`itinerary_id`),
  KEY `IDX_8AB0BC1DA76ED395` (`user_id`),
  CONSTRAINT `FK_8AB0BC1D15F737B2` FOREIGN KEY (`itinerary_id`) REFERENCES `itinerary` (`id`),
  CONSTRAINT `FK_8AB0BC1DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library`
--

DROP TABLE IF EXISTS `library`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` longtext DEFAULT NULL,
  `type` varchar(10) NOT NULL,
  `enable_rating` tinyint(1) NOT NULL,
  `enable_comments` tinyint(1) NOT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `locale` varchar(4) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A18098BC12469DE2` (`category_id`),
  KEY `IDX_A18098BCB03A8386` (`created_by_id`),
  KEY `IDX_A18098BC896DBBDE` (`updated_by_id`),
  KEY `IDX_A18098BCC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_A18098BC12469DE2` FOREIGN KEY (`category_id`) REFERENCES `library_category` (`id`),
  CONSTRAINT `FK_A18098BC896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_A18098BCB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_A18098BCC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_category`
--

DROP TABLE IF EXISTS `library_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_20EFE8D5B03A8386` (`created_by_id`),
  KEY `IDX_20EFE8D5896DBBDE` (`updated_by_id`),
  KEY `IDX_20EFE8D5C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_20EFE8D5896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_20EFE8D5B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_20EFE8D5C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_category_translation`
--

DROP TABLE IF EXISTS `library_category_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_category_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `library_category_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_F72523D02C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_F72523D02C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `library_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_comment`
--

DROP TABLE IF EXISTS `library_comment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_comment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `library_id` int(11) NOT NULL,
  `allowed_by_id` int(11) DEFAULT NULL,
  `banned_by_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `comment` longtext NOT NULL,
  `rating` smallint(6) NOT NULL,
  `visible` tinyint(1) NOT NULL,
  `allowed_at` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  `banned_at` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_EEB5EA5CFE2541D7` (`library_id`),
  KEY `IDX_EEB5EA5C69463ADC` (`allowed_by_id`),
  KEY `IDX_EEB5EA5C386B8E7` (`banned_by_id`),
  KEY `IDX_EEB5EA5CB03A8386` (`created_by_id`),
  KEY `IDX_EEB5EA5C896DBBDE` (`updated_by_id`),
  KEY `IDX_EEB5EA5CC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_EEB5EA5C386B8E7` FOREIGN KEY (`banned_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_EEB5EA5C69463ADC` FOREIGN KEY (`allowed_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_EEB5EA5C896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_EEB5EA5CB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_EEB5EA5CC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_EEB5EA5CFE2541D7` FOREIGN KEY (`library_id`) REFERENCES `library` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_file`
--

DROP TABLE IF EXISTS `library_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `library_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `filename` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_2A50FC7DFE2541D7` (`library_id`),
  CONSTRAINT `FK_2A50FC7DFE2541D7` FOREIGN KEY (`library_id`) REFERENCES `library` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_filter`
--

DROP TABLE IF EXISTS `library_filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_filter` (
  `library_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL,
  PRIMARY KEY (`library_id`,`filter_id`),
  KEY `IDX_D426C796FE2541D7` (`library_id`),
  KEY `IDX_D426C796D395B25E` (`filter_id`),
  CONSTRAINT `FK_D426C796D395B25E` FOREIGN KEY (`filter_id`) REFERENCES `filter` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_D426C796FE2541D7` FOREIGN KEY (`library_id`) REFERENCES `library` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_video`
--

DROP TABLE IF EXISTS `library_video`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `library_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) NOT NULL,
  `url` varchar(255) NOT NULL,
  `source` varchar(20) NOT NULL,
  `identifier` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_4F620803FE2541D7` (`library_id`),
  CONSTRAINT `FK_4F620803FE2541D7` FOREIGN KEY (`library_id`) REFERENCES `library` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_views`
--

DROP TABLE IF EXISTS `library_views`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_views` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `library_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  PRIMARY KEY (`id`),
  KEY `IDX_22554EA8A76ED395` (`user_id`),
  KEY `IDX_22554EA8FE2541D7` (`library_id`),
  CONSTRAINT `FK_22554EA8A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_22554EA8FE2541D7` FOREIGN KEY (`library_id`) REFERENCES `library` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `main_course_evaluation`
--

DROP TABLE IF EXISTS `main_course_evaluation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `main_course_evaluation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `is_main_nps` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_ABF7398B591CC992` (`course_id`),
  CONSTRAINT `FK_ABF7398B591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `manager_filter`
--

DROP TABLE IF EXISTS `manager_filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `manager_filter` (
  `user_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`filter_id`),
  KEY `IDX_B9E1596FA76ED395` (`user_id`),
  KEY `IDX_B9E1596FD395B25E` (`filter_id`),
  CONSTRAINT `FK_B9E1596FA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_B9E1596FD395B25E` FOREIGN KEY (`filter_id`) REFERENCES `filter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `material_course`
--

DROP TABLE IF EXISTS `material_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `material_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `announcement_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `mime_type` varchar(255) DEFAULT NULL,
  `type_material` varchar(255) NOT NULL,
  `url_material` longtext DEFAULT NULL,
  `is_download` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_87ACE482591CC992` (`course_id`),
  KEY `IDX_87ACE482913AEA17` (`announcement_id`),
  KEY `IDX_87ACE482B03A8386` (`created_by_id`),
  KEY `IDX_87ACE482896DBBDE` (`updated_by_id`),
  KEY `IDX_87ACE482C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_87ACE482591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_87ACE482896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_87ACE482913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_87ACE482B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_87ACE482C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `material_download_history`
--

DROP TABLE IF EXISTS `material_download_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `material_download_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `material_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_7180BF5B591CC992` (`course_id`),
  KEY `IDX_7180BF5BA76ED395` (`user_id`),
  KEY `IDX_7180BF5BE308AC6F` (`material_id`),
  KEY `IDX_7180BF5BB03A8386` (`created_by_id`),
  KEY `IDX_7180BF5B896DBBDE` (`updated_by_id`),
  KEY `IDX_7180BF5BC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_7180BF5B591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_7180BF5B896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7180BF5BA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7180BF5BB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7180BF5BC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_7180BF5BE308AC6F` FOREIGN KEY (`material_id`) REFERENCES `material_course` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `message`
--

DROP TABLE IF EXISTS `message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `reply_to_id` int(11) DEFAULT NULL,
  `subject` varchar(255) NOT NULL,
  `body` longtext NOT NULL,
  `open_at` datetime DEFAULT NULL,
  `sent_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B6BD307FF624B39D` (`sender_id`),
  KEY `IDX_B6BD307FE92F8F78` (`recipient_id`),
  KEY `IDX_B6BD307FFFDF7169` (`reply_to_id`),
  CONSTRAINT `FK_B6BD307FE92F8F78` FOREIGN KEY (`recipient_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_B6BD307FF624B39D` FOREIGN KEY (`sender_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_B6BD307FFFDF7169` FOREIGN KEY (`reply_to_id`) REFERENCES `message` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `message_attachment`
--

DROP TABLE IF EXISTS `message_attachment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `message_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `filename` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `mime_type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B68FF524537A1329` (`message_id`),
  CONSTRAINT `FK_B68FF524537A1329` FOREIGN KEY (`message_id`) REFERENCES `message` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `migration_versions`
--

DROP TABLE IF EXISTS `migration_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migration_versions` (
  `version` varchar(191) NOT NULL,
  `executed_at` datetime DEFAULT NULL,
  `execution_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `news`
--

DROP TABLE IF EXISTS `news`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `title` text NOT NULL,
  `text` text NOT NULL,
  `locale` varchar(10) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_1DD39950B03A8386` (`created_by_id`),
  KEY `IDX_1DD39950896DBBDE` (`updated_by_id`),
  KEY `IDX_1DD39950C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_1DD39950896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_1DD39950B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_1DD39950C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `news_course_section`
--

DROP TABLE IF EXISTS `news_course_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `news_course_section` (
  `news_id` int(11) NOT NULL,
  `course_section_id` int(11) NOT NULL,
  PRIMARY KEY (`news_id`,`course_section_id`),
  KEY `IDX_BA5A78CAB5A459A0` (`news_id`),
  KEY `IDX_BA5A78CA7C1ADF9` (`course_section_id`),
  CONSTRAINT `FK_BA5A78CA7C1ADF9` FOREIGN KEY (`course_section_id`) REFERENCES `course_section` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_BA5A78CAB5A459A0` FOREIGN KEY (`news_id`) REFERENCES `news` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `news_translation`
--

DROP TABLE IF EXISTS `news_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `news_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `translatable_id` int(11) DEFAULT NULL,
  `title` longtext DEFAULT NULL,
  `text` longtext DEFAULT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `news_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_9D5CF3202C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_9D5CF3202C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `news` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `notification`
--

DROP TABLE IF EXISTS `notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` longtext DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `read_at` datetime DEFAULT NULL,
  `data` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  KEY `IDX_BF5476CAA76ED395` (`user_id`),
  CONSTRAINT `FK_BF5476CAA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `nps`
--

DROP TABLE IF EXISTS `nps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nps` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `value` longtext DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `to_post` tinyint(1) NOT NULL,
  `main` tinyint(1) DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_5B3B66481E27F6BF` (`question_id`),
  KEY `IDX_5B3B6648591CC992` (`course_id`),
  KEY `IDX_5B3B6648B03A8386` (`created_by_id`),
  KEY `IDX_5B3B6648896DBBDE` (`updated_by_id`),
  KEY `IDX_5B3B6648C76F1F52` (`deleted_by_id`),
  KEY `IDX_5B3B6648A76ED395` (`user_id`),
  CONSTRAINT `FK_5B3B66481E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `nps_question` (`id`),
  CONSTRAINT `FK_5B3B6648591CC992` FOREIGN KEY (`course_id`) REFERENCES `user_course` (`id`),
  CONSTRAINT `FK_5B3B6648896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_5B3B6648A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_5B3B6648B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_5B3B6648C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `nps_question`
--

DROP TABLE IF EXISTS `nps_question`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nps_question` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `main` tinyint(1) NOT NULL,
  `question` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `position` int(11) NOT NULL,
  `source` int(11) NOT NULL,
  `entity_subsidizer` varchar(255) DEFAULT NULL,
  `survey_id` int(11) DEFAULT NULL,
  `active` tinyint(1) NOT NULL,
  `random_order` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_27D40D91B03A8386` (`created_by_id`),
  KEY `IDX_27D40D91896DBBDE` (`updated_by_id`),
  KEY `IDX_27D40D91C76F1F52` (`deleted_by_id`),
  KEY `IDX_27D40D91B3FE509D` (`survey_id`),
  CONSTRAINT `FK_27D40D91896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_27D40D91B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_27D40D91B3FE509D` FOREIGN KEY (`survey_id`) REFERENCES `survey` (`id`),
  CONSTRAINT `FK_27D40D91C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `nps_question_announcement`
--

DROP TABLE IF EXISTS `nps_question_announcement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nps_question_announcement` (
  `nps_question_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  PRIMARY KEY (`nps_question_id`,`announcement_id`),
  KEY `IDX_52B16383AE807F50` (`nps_question_id`),
  KEY `IDX_52B16383913AEA17` (`announcement_id`),
  CONSTRAINT `FK_52B16383913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_52B16383AE807F50` FOREIGN KEY (`nps_question_id`) REFERENCES `nps_question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `nps_question_course`
--

DROP TABLE IF EXISTS `nps_question_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nps_question_course` (
  `nps_question_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  PRIMARY KEY (`nps_question_id`,`course_id`),
  KEY `IDX_5B1CA872AE807F50` (`nps_question_id`),
  KEY `IDX_5B1CA872591CC992` (`course_id`),
  CONSTRAINT `FK_5B1CA872591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_5B1CA872AE807F50` FOREIGN KEY (`nps_question_id`) REFERENCES `nps_question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `nps_question_detail`
--

DROP TABLE IF EXISTS `nps_question_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nps_question_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nps_question_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `value` longtext NOT NULL,
  `position` smallint(6) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_6384B858AE807F50` (`nps_question_id`),
  KEY `IDX_6384B858B03A8386` (`created_by_id`),
  KEY `IDX_6384B858896DBBDE` (`updated_by_id`),
  KEY `IDX_6384B858C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_6384B858896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_6384B858AE807F50` FOREIGN KEY (`nps_question_id`) REFERENCES `nps_question` (`id`),
  CONSTRAINT `FK_6384B858B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_6384B858C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `nps_question_translation`
--

DROP TABLE IF EXISTS `nps_question_translation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nps_question_translation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question` longtext DEFAULT NULL,
  `translatable_id` int(11) DEFAULT NULL,
  `locale` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nps_question_translation_unique_translation` (`translatable_id`,`locale`),
  KEY `IDX_6650B4BC2C2AC5D3` (`translatable_id`),
  CONSTRAINT `FK_6650B4BC2C2AC5D3` FOREIGN KEY (`translatable_id`) REFERENCES `nps_question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ordenar_menormayor`
--

DROP TABLE IF EXISTS `ordenar_menormayor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ordenar_menormayor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `words_array` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `time` int(11) NOT NULL,
  `gametype` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_C414560B579F4768` (`chapter_id`),
  CONSTRAINT `FK_C414560B579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parejas`
--

DROP TABLE IF EXISTS `parejas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parejas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `tipo` int(11) NOT NULL,
  `tiempo` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_CDEC53A2579F4768` (`chapter_id`),
  CONSTRAINT `FK_CDEC53A2579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parejas_imagen`
--

DROP TABLE IF EXISTS `parejas_imagen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parejas_imagen` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parejas_id` int(11) DEFAULT NULL,
  `texto` varchar(255) NOT NULL,
  `imagen` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_4907912B8497EC37` (`parejas_id`),
  CONSTRAINT `FK_4907912B8497EC37` FOREIGN KEY (`parejas_id`) REFERENCES `parejas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdf`
--

DROP TABLE IF EXISTS `pdf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdf` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) NOT NULL,
  `pdf` varchar(255) DEFAULT NULL,
  `is_downloadable` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_EF0DB8C579F4768` (`chapter_id`),
  CONSTRAINT `FK_EF0DB8C579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `professional_category`
--

DROP TABLE IF EXISTS `professional_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `professional_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_95CAC37C727ACA70` (`parent_id`),
  CONSTRAINT `FK_95CAC37C727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `professional_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `puzzle`
--

DROP TABLE IF EXISTS `puzzle`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `puzzle` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_22A6DFDF579F4768` (`chapter_id`),
  CONSTRAINT `FK_22A6DFDF579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `question`
--

DROP TABLE IF EXISTS `question`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `question` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `question` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `random` int(11) DEFAULT NULL,
  `is_feedback` tinyint(1) DEFAULT NULL,
  `feedback_positive` varchar(255) DEFAULT NULL,
  `feedback_negative` varchar(255) DEFAULT NULL,
  `time` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B6F7494E579F4768` (`chapter_id`),
  KEY `IDX_B6F7494EB03A8386` (`created_by_id`),
  KEY `IDX_B6F7494E896DBBDE` (`updated_by_id`),
  KEY `IDX_B6F7494EC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_B6F7494E579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_B6F7494E896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_B6F7494EB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_B6F7494EC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `questions_announcement`
--

DROP TABLE IF EXISTS `questions_announcement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `questions_announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section` varchar(255) NOT NULL,
  `section_json` longtext DEFAULT NULL,
  `questions_json` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recovery_code`
--

DROP TABLE IF EXISTS `recovery_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recovery_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `date_recovery` date NOT NULL,
  `code_activation` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `state` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_2C8D0584A76ED395` (`user_id`),
  CONSTRAINT `FK_2C8D0584A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `refresh_tokens`
--

DROP TABLE IF EXISTS `refresh_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `refresh_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `refresh_token` varchar(128) NOT NULL,
  `username` varchar(255) NOT NULL,
  `valid` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_9BACE7E1C74F2195` (`refresh_token`)
) ENGINE=InnoDB AUTO_INCREMENT=172 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `reset_password_request`
--

DROP TABLE IF EXISTS `reset_password_request`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reset_password_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `selector` varchar(20) NOT NULL,
  `hashed_token` varchar(100) NOT NULL,
  `requested_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `expires_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  PRIMARY KEY (`id`),
  KEY `IDX_7CE748AA76ED395` (`user_id`),
  CONSTRAINT `FK_7CE748AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roleplay_beginning`
--

DROP TABLE IF EXISTS `roleplay_beginning`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roleplay_beginning` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL,
  `code` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `background` longtext DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `order` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_E89A902B166D1F9C` (`project_id`),
  CONSTRAINT `FK_E89A902B166D1F9C` FOREIGN KEY (`project_id`) REFERENCES `roleplay_project` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roleplay_ending`
--

DROP TABLE IF EXISTS `roleplay_ending`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roleplay_ending` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL,
  `code` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `background` longtext DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `order` int(11) NOT NULL,
  `approval_by_points` int(11) DEFAULT NULL,
  `approval_by_history` tinyint(1) NOT NULL,
  `approval_type` varchar(60) DEFAULT NULL,
  `statement` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3A4A6E5B166D1F9C` (`project_id`),
  CONSTRAINT `FK_3A4A6E5B166D1F9C` FOREIGN KEY (`project_id`) REFERENCES `roleplay_project` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roleplay_project`
--

DROP TABLE IF EXISTS `roleplay_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roleplay_project` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roleplay_scene`
--

DROP TABLE IF EXISTS `roleplay_scene`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roleplay_scene` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sequence_id` int(11) NOT NULL,
  `code` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `attached_tag` longtext DEFAULT NULL,
  `attached` longtext DEFAULT NULL,
  `background` longtext DEFAULT NULL,
  `avatar` longtext DEFAULT NULL,
  `video` longtext DEFAULT NULL,
  `standby` longtext DEFAULT NULL,
  `statement` longtext DEFAULT NULL,
  `order` int(11) NOT NULL,
  `answers` longtext NOT NULL COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  KEY `IDX_34CE7AA798FB19AE` (`sequence_id`),
  CONSTRAINT `FK_34CE7AA798FB19AE` FOREIGN KEY (`sequence_id`) REFERENCES `roleplay_sequence` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roleplay_sequence`
--

DROP TABLE IF EXISTS `roleplay_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roleplay_sequence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL,
  `color` varchar(255) NOT NULL,
  `order` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_579C74B5166D1F9C` (`project_id`),
  CONSTRAINT `FK_579C74B5166D1F9C` FOREIGN KEY (`project_id`) REFERENCES `roleplay_project` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roulette_word`
--

DROP TABLE IF EXISTS `roulette_word`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roulette_word` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `letter` varchar(2) NOT NULL,
  `word` varchar(255) DEFAULT NULL,
  `question` varchar(255) DEFAULT NULL,
  `type` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_EDB2A53C579F4768` (`chapter_id`),
  CONSTRAINT `FK_EDB2A53C579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scorm`
--

DROP TABLE IF EXISTS `scorm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scorm` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) NOT NULL,
  `folder` varchar(255) NOT NULL,
  `entry_point` varchar(255) DEFAULT NULL,
  `menu` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  `show_menu` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_3C42BF63579F4768` (`chapter_id`),
  CONSTRAINT `FK_3C42BF63579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `season`
--

DROP TABLE IF EXISTS `season`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `season` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `sort` int(11) NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'sequential',
  PRIMARY KEY (`id`),
  KEY `IDX_F0E45BA9591CC992` (`course_id`),
  CONSTRAINT `FK_F0E45BA9591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sessions_announcement`
--

DROP TABLE IF EXISTS `sessions_announcement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions_announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announcement_id` int(11) DEFAULT NULL,
  `hour_session` int(11) DEFAULT NULL,
  `name_announcement` varchar(255) DEFAULT NULL,
  `classroom` varchar(255) DEFAULT NULL,
  `num_sessions` int(11) DEFAULT NULL,
  `extra` longtext DEFAULT NULL,
  `assistance` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_6A027C4B913AEA17` (`announcement_id`),
  CONSTRAINT `FK_6A027C4B913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `slider`
--

DROP TABLE IF EXISTS `slider`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `slider` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `position` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_CFC71007579F4768` (`chapter_id`),
  KEY `IDX_CFC71007B03A8386` (`created_by_id`),
  KEY `IDX_CFC71007896DBBDE` (`updated_by_id`),
  KEY `IDX_CFC71007C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_CFC71007579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_CFC71007896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_CFC71007B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_CFC71007C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `survey`
--

DROP TABLE IF EXISTS `survey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `survey` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `description` longtext DEFAULT NULL,
  `apply_to` smallint(6) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_AD5F9BFCB03A8386` (`created_by_id`),
  KEY `IDX_AD5F9BFC896DBBDE` (`updated_by_id`),
  KEY `IDX_AD5F9BFCC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_AD5F9BFC896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_AD5F9BFCB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_AD5F9BFCC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `survey_announcement`
--

DROP TABLE IF EXISTS `survey_announcement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `survey_announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `survey_id` int(11) NOT NULL,
  `announcement_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3D6BD54CB3FE509D` (`survey_id`),
  KEY `IDX_3D6BD54C913AEA17` (`announcement_id`),
  CONSTRAINT `FK_3D6BD54C913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_3D6BD54CB3FE509D` FOREIGN KEY (`survey_id`) REFERENCES `survey` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `survey_course`
--

DROP TABLE IF EXISTS `survey_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `survey_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `survey_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3E90A7D5B3FE509D` (`survey_id`),
  KEY `IDX_3E90A7D5591CC992` (`course_id`),
  CONSTRAINT `FK_3E90A7D5591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_3E90A7D5B3FE509D` FOREIGN KEY (`survey_id`) REFERENCES `survey` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tag`
--

DROP TABLE IF EXISTS `tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task`
--

DROP TABLE IF EXISTS `task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task` varchar(255) NOT NULL,
  `params` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  `status` smallint(6) NOT NULL,
  `created_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `started_at` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  `finished_at` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_course`
--

DROP TABLE IF EXISTS `task_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `announcement_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` longtext NOT NULL,
  `date_delivery` datetime NOT NULL,
  `date_delivery_announcement` datetime DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `is_visible` tinyint(1) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_2D37EB6A591CC992` (`course_id`),
  KEY `IDX_2D37EB6A913AEA17` (`announcement_id`),
  KEY `IDX_2D37EB6AB03A8386` (`created_by_id`),
  KEY `IDX_2D37EB6A896DBBDE` (`updated_by_id`),
  KEY `IDX_2D37EB6AC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_2D37EB6A591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_2D37EB6A896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2D37EB6A913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_2D37EB6AB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2D37EB6AC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_user`
--

DROP TABLE IF EXISTS `task_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_FE204232A76ED395` (`user_id`),
  KEY `IDX_FE2042328DB60186` (`task_id`),
  KEY `IDX_FE204232B03A8386` (`created_by_id`),
  KEY `IDX_FE204232896DBBDE` (`updated_by_id`),
  KEY `IDX_FE204232C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_FE204232896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FE2042328DB60186` FOREIGN KEY (`task_id`) REFERENCES `task_course` (`id`),
  CONSTRAINT `FK_FE204232A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FE204232B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_FE204232C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `time_game`
--

DROP TABLE IF EXISTS `time_game`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `time_game` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `time` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_8E9638A1579F4768` (`chapter_id`),
  CONSTRAINT `FK_8E9638A1579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `true_or_false`
--

DROP TABLE IF EXISTS `true_or_false`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `true_or_false` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `route` varchar(255) DEFAULT NULL,
  `text` varchar(255) NOT NULL,
  `result` tinyint(1) NOT NULL,
  `trueorfalse_id` int(11) DEFAULT NULL,
  `time` int(11) DEFAULT NULL,
  `categorized` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_CA13A6C3579F4768` (`chapter_id`),
  CONSTRAINT `FK_CA13A6C3579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `type_video`
--

DROP TABLE IF EXISTS `type_video`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `type_video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `url_shortener`
--

DROP TABLE IF EXISTS `url_shortener`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `url_shortener` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `long_url` longtext NOT NULL,
  `short_url` varchar(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(180) NOT NULL,
  `roles` longtext NOT NULL COMMENT '(DC2Type:json)',
  `password` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `code` varchar(255) DEFAULT NULL,
  `points` int(11) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `locale` varchar(10) DEFAULT NULL,
  `data_avatar` varchar(400) DEFAULT NULL,
  `register_key` varchar(255) DEFAULT NULL,
  `validated` tinyint(1) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `open` tinyint(1) NOT NULL,
  `meta` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  `team_manager_id` int(11) DEFAULT NULL,
  `team_manager_email` varchar(255) DEFAULT NULL,
  `starteam` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_8D93D649E7927C74` (`email`),
  KEY `IDX_8D93D649B03A8386` (`created_by_id`),
  KEY `IDX_8D93D649896DBBDE` (`updated_by_id`),
  KEY `IDX_8D93D649C76F1F52` (`deleted_by_id`),
  KEY `IDX_8D93D64946E746A6` (`team_manager_id`),
  CONSTRAINT `FK_8D93D64946E746A6` FOREIGN KEY (`team_manager_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_8D93D649896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_8D93D649B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_8D93D649C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=111 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_comments`
--

DROP TABLE IF EXISTS `user_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `text` longtext NOT NULL,
  `type` tinyint(1) NOT NULL,
  `send_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BF13722AA76ED395` (`user_id`),
  CONSTRAINT `FK_BF13722A9D86650F` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_course`
--

DROP TABLE IF EXISTS `user_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_course` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `announcement_id` int(11) DEFAULT NULL,
  `started_at` datetime NOT NULL,
  `finished_at` datetime DEFAULT NULL,
  `valued_at` datetime DEFAULT NULL,
  `time_spent` int(11) DEFAULT NULL,
  `points` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_73CC7484A76ED395` (`user_id`),
  KEY `IDX_73CC7484591CC992` (`course_id`),
  KEY `IDX_73CC7484913AEA17` (`announcement_id`),
  CONSTRAINT `FK_73CC7484591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_73CC7484913AEA17` FOREIGN KEY (`announcement_id`) REFERENCES `announcement` (`id`),
  CONSTRAINT `FK_73CC7484A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_course_chapter`
--

DROP TABLE IF EXISTS `user_course_chapter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_course_chapter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_course_id` int(11) NOT NULL,
  `chapter_id` int(11) NOT NULL,
  `started_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `finished_at` datetime DEFAULT NULL,
  `data` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  `time_spent` int(11) DEFAULT NULL,
  `points` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_9AC76F9B59FC4476` (`user_course_id`),
  KEY `IDX_9AC76F9B579F4768` (`chapter_id`),
  CONSTRAINT `FK_9AC76F9B579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_9AC76F9B59FC4476` FOREIGN KEY (`user_course_id`) REFERENCES `user_course` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_courses_total_time`
--

DROP TABLE IF EXISTS `user_courses_total_time`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_courses_total_time` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `updated_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_B7D6BB96A76ED395` (`user_id`),
  CONSTRAINT `FK_B7D6BB96A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_extra`
--

DROP TABLE IF EXISTS `user_extra`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_extra` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `center_id` int(11) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `birthdate` date DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `resume` varchar(255) DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `pdf_cv` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_AFFDF63DA76ED395` (`user_id`),
  KEY `IDX_AFFDF63D12469DE2` (`category_id`),
  KEY `IDX_AFFDF63DAE80F5DF` (`department_id`),
  KEY `IDX_AFFDF63D5932F377` (`center_id`),
  KEY `IDX_AFFDF63DB03A8386` (`created_by_id`),
  KEY `IDX_AFFDF63D896DBBDE` (`updated_by_id`),
  KEY `IDX_AFFDF63DC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_AFFDF63D12469DE2` FOREIGN KEY (`category_id`) REFERENCES `professional_category` (`id`),
  CONSTRAINT `FK_AFFDF63D5932F377` FOREIGN KEY (`center_id`) REFERENCES `center` (`id`),
  CONSTRAINT `FK_AFFDF63D896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_AFFDF63DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_AFFDF63DAE80F5DF` FOREIGN KEY (`department_id`) REFERENCES `department` (`id`),
  CONSTRAINT `FK_AFFDF63DB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_AFFDF63DC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_filter`
--

DROP TABLE IF EXISTS `user_filter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_filter` (
  `user_id` int(11) NOT NULL,
  `filter_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`filter_id`),
  KEY `IDX_1A964420A76ED395` (`user_id`),
  KEY `IDX_1A964420D395B25E` (`filter_id`),
  CONSTRAINT `FK_1A964420A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_1A964420D395B25E` FOREIGN KEY (`filter_id`) REFERENCES `filter` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_login`
--

DROP TABLE IF EXISTS `user_login`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_login` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `device` varchar(100) DEFAULT NULL,
  `browser` varchar(100) DEFAULT NULL,
  `platform` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_48CA3048A76ED395` (`user_id`),
  CONSTRAINT `FK_48CA3048A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=118 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_manage`
--

DROP TABLE IF EXISTS `user_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_manage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `centers` longtext NOT NULL COMMENT '(DC2Type:json)',
  `countries` longtext NOT NULL COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_4120B177A76ED395` (`user_id`),
  CONSTRAINT `FK_4120B177A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_roleplay_project`
--

DROP TABLE IF EXISTS `user_roleplay_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roleplay_project` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `user_course_chapter_id` int(11) NOT NULL,
  `project_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `answers` longtext NOT NULL COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_48521FF47062B2C0` (`user_course_chapter_id`),
  KEY `IDX_48521FF4A76ED395` (`user_id`),
  KEY `IDX_48521FF4166D1F9C` (`project_id`),
  KEY `IDX_48521FF4B03A8386` (`created_by_id`),
  KEY `IDX_48521FF4896DBBDE` (`updated_by_id`),
  KEY `IDX_48521FF4C76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_48521FF4166D1F9C` FOREIGN KEY (`project_id`) REFERENCES `roleplay_project` (`id`),
  CONSTRAINT `FK_48521FF47062B2C0` FOREIGN KEY (`user_course_chapter_id`) REFERENCES `user_course_chapter` (`id`),
  CONSTRAINT `FK_48521FF4896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_48521FF4A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_48521FF4B03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_48521FF4C76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_time`
--

DROP TABLE IF EXISTS `user_time`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_time` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `course_id` int(11) DEFAULT NULL,
  `created_at` date NOT NULL,
  `name` varchar(100) NOT NULL,
  `time` int(11) NOT NULL,
  `extra` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  KEY `IDX_1515D48CA76ED395` (`user_id`),
  KEY `IDX_1515D48C591CC992` (`course_id`),
  CONSTRAINT `FK_1515D48C591CC992` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`),
  CONSTRAINT `FK_1515D48CA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_token`
--

DROP TABLE IF EXISTS `user_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_token` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` smallint(6) NOT NULL,
  `token` varchar(255) NOT NULL,
  `revoked` tinyint(1) NOT NULL,
  `used` tinyint(1) NOT NULL,
  `used_at` datetime DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
  `valid_until` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  `extra` longtext DEFAULT NULL COMMENT '(DC2Type:json)',
  `created_at` datetime NOT NULL COMMENT '(DC2Type:datetime_immutable)',
  PRIMARY KEY (`id`),
  KEY `IDX_BDF55A63A76ED395` (`user_id`),
  CONSTRAINT `FK_BDF55A63A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_vcms_project`
--

DROP TABLE IF EXISTS `user_vcms_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_vcms_project` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `project_id` int(11) NOT NULL,
  `user_course_chapter_id` int(11) NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `updated_by_id` int(11) DEFAULT NULL,
  `deleted_by_id` int(11) DEFAULT NULL,
  `actions_data` longtext NOT NULL COMMENT '(DC2Type:json)',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_2AB6F06A7062B2C0` (`user_course_chapter_id`),
  KEY `IDX_2AB6F06AA76ED395` (`user_id`),
  KEY `IDX_2AB6F06A166D1F9C` (`project_id`),
  KEY `IDX_2AB6F06AB03A8386` (`created_by_id`),
  KEY `IDX_2AB6F06A896DBBDE` (`updated_by_id`),
  KEY `IDX_2AB6F06AC76F1F52` (`deleted_by_id`),
  CONSTRAINT `FK_2AB6F06A166D1F9C` FOREIGN KEY (`project_id`) REFERENCES `vcms_project` (`id`),
  CONSTRAINT `FK_2AB6F06A7062B2C0` FOREIGN KEY (`user_course_chapter_id`) REFERENCES `user_course_chapter` (`id`),
  CONSTRAINT `FK_2AB6F06A896DBBDE` FOREIGN KEY (`updated_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2AB6F06AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2AB6F06AB03A8386` FOREIGN KEY (`created_by_id`) REFERENCES `user` (`id`),
  CONSTRAINT `FK_2AB6F06AC76F1F52` FOREIGN KEY (`deleted_by_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vcms_project`
--

DROP TABLE IF EXISTS `vcms_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `vcms_project` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slides` longtext NOT NULL COMMENT '(DC2Type:json)',
  `view` varchar(40) DEFAULT NULL,
  `progressive` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `video`
--

DROP TABLE IF EXISTS `video`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_video_id` int(11) NOT NULL,
  `chapter_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `url_video` varchar(255) NOT NULL,
  `origen` varchar(255) DEFAULT NULL,
  `identifier` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_7CC7DA2C579F4768` (`chapter_id`),
  KEY `IDX_7CC7DA2CE43E27B2` (`type_video_id`),
  CONSTRAINT `FK_7CC7DA2C579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`),
  CONSTRAINT `FK_7CC7DA2CE43E27B2` FOREIGN KEY (`type_video_id`) REFERENCES `type_video` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videopreguntas`
--

DROP TABLE IF EXISTS `videopreguntas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `videopreguntas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `videoquiz_id` int(11) DEFAULT NULL,
  `currenttime` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `texto` varchar(255) DEFAULT NULL,
  `respuestas` longtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BE3B3F894458963B` (`videoquiz_id`),
  CONSTRAINT `FK_BE3B3F894458963B` FOREIGN KEY (`videoquiz_id`) REFERENCES `videoquiz` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videoquiz`
--

DROP TABLE IF EXISTS `videoquiz`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `videoquiz` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chapter_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `url` varchar(255) NOT NULL,
  `video_duration` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_13E0266C579F4768` (`chapter_id`),
  CONSTRAINT `FK_13E0266C579F4768` FOREIGN KEY (`chapter_id`) REFERENCES `chapter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'ecovalores_pro'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-05 16:07:00
